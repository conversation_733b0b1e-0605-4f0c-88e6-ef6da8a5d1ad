package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.math.BigInteger;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingQueue;
import org.springframework.stereotype.Component;
import org.web3j.abi.EventValues;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Type;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.methods.response.EthBlock;
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt;
import org.web3j.protocol.core.methods.response.EthLog;
import org.web3j.protocol.core.methods.response.Log;
import org.web3j.protocol.core.methods.response.TransactionReceipt;
import org.web3j.tx.Contract;

@Component
public class EthEventLogDao {
  public static final long RETRY_WAIT_SECONDS = 3L;
  private final LoggingService logger;
  private final BcmonitoringConfigurationProperties properties;
  private final Web3jConfig web3jConfig;
  private final EventSignatureRegistry eventRegistry;
  private final AbiParser abiParser;
  private final ObjectMapper objectMapper;

  /**
   * Constructor for EthEventLogDao.
   *
   * @param log The logging service.
   * @param properties The configuration properties.
   * @param web3jConfig The Web3j configuration.
   * @param eventRegistry The event signature registry.
   * @param abiParser The ABI parser.
   * @param objectMapper The object mapper.
   */
  public EthEventLogDao(
      LoggingService log,
      BcmonitoringConfigurationProperties properties,
      Web3jConfig web3jConfig,
      EventSignatureRegistry eventRegistry,
      AbiParser abiParser,
      ObjectMapper objectMapper) {
    this.logger = log;
    this.properties = properties;
    this.web3jConfig = web3jConfig;
    this.eventRegistry = eventRegistry;
    this.abiParser = abiParser;
    this.objectMapper = objectMapper;
  }

  /**
   * Subscribes to all blocks and processes transactions.
   *
   * @return A BlockingQueue of Transaction objects.
   */
  public BlockingQueue<Transaction> subscribeAll() {
    BlockingQueue<Transaction> transactions = new LinkedBlockingQueue<>();

    // Check if the difference is valid
    int allowableDiff;
    try {
      allowableDiff =
          Integer.parseInt(properties.getSubscription().getAllowableBlockTimestampDiffSec());
    } catch (NumberFormatException e) {
      logger.error("Failed to parse allowable timestamp difference", e);
      return null;
    }

    try {
      // Create a new Web3j instance for this subscription
      Web3j web3j = web3jConfig.createWebSocketWeb3j();

      try {

        // Subscribe to new blocks
        web3j
            .blockFlowable(false)
            .subscribe(
                blockObject -> {
                  try {
                    EthBlock.Block block = blockObject.getBlock();
                    BigInteger blockNumber = block.getNumber();

                    // Check delay in block processing
                    if (isDelayed(block, allowableDiff)) {
                      logger.warn(
                          "Block {} is delayed by more than {} seconds",
                          blockNumber,
                          allowableDiff);
                      return;
                    }

                    // Process block transactions and events
                    List<Event> events = convBlock2EventEntities(block);
                    if (!events.isEmpty()) {

                      BlockHeight blockHeight =
                          BlockHeight.builder().blockNumber(blockNumber.longValue()).build();
                      Transaction transaction =
                          Transaction.builder().events(events).blockHeight(blockHeight).build();

                      transactions.offer(transaction);
                    }
                  } catch (Exception e) {
                    logger.error("Error processing block", e);
                  }
                },
                error -> {
                  logger.error("Subscription error", error);
                },
                () -> logger.info("Subscription completed"));

        return transactions;
      } finally {
        // Shutdown the Web3j instance to free resources
        web3j.shutdown();
      }
    } catch (Exception e) {
      logger.error("Failed to create Web3j subscription", e);
      return transactions;
    }
  }

  /**
   * Checks if the block is delayed based on the allowable difference in seconds.
   *
   * @param block The block to check.
   * @param allowableDiffSeconds The allowable difference in seconds.
   * @return true if the block is delayed, false otherwise.
   */
  private boolean isDelayed(EthBlock.Block block, int allowableDiffSeconds) {
    long blockTimestamp = block.getTimestamp().longValue();
    long currentTime = Instant.now().getEpochSecond();
    long diff = currentTime - blockTimestamp;

    return diff > allowableDiffSeconds;
  }

  /**
   * Converts a block to a collection of event entities.
   *
   * @param block Ethereum block
   * @return Collection of events found in the block
   * @throws IOException If there is an error communicating with the Ethereum node
   * @throws ExecutionException If there is an error executing the transaction
   * @throws InterruptedException If the operation is interrupted
   */
  public List<Event> convBlock2EventEntities(EthBlock.Block block) {
    List<Event> events = new ArrayList<>();

    try {
      // Create a new Web3j instance for this operation
      Web3j web3j = web3jConfig.createWebSocketWeb3j();

      try {

        for (EthBlock.TransactionResult txResult : block.getTransactions()) {
          try {
            EthBlock.TransactionObject tx = (EthBlock.TransactionObject) txResult;
            EthGetTransactionReceipt receiptResponse =
                web3j.ethGetTransactionReceipt(tx.getHash()).sendAsync().get();

            TransactionReceipt receipt = receiptResponse.getTransactionReceipt().orElse(null);
            if (receipt == null) {
              continue;
            }

            for (Log log : receipt.getLogs()) {
              try {
                logger.info("event found tx_hash={}", log.getTransactionHash());
                Event event = convertEthLogToEventEntity(log);
                logger.info("event parsed tx_hash={}, name={}", event.transactionHash, event.name);

                if (event.transactionHash != null && !event.transactionHash.isEmpty()) {
                  events.add(event);
                }
              } catch (Exception e) {
                logger.error("Error processing log for transaction {}", log.getTransactionHash());
              }
            }
          } catch (Exception e) {
            logger.error("Error processing transaction", e);
          }
        }

      } finally {
        // Shutdown the Web3j instance to free resources
        web3j.shutdown();
      }
    } catch (Exception e) {
      logger.error("Error creating Web3j instance", e);
    }

    return events;
  }

  /**
   * Converts an Ethereum log to an Event entity.
   *
   * @param ethLog The Ethereum log to convert
   * @return Converted Event entity
   * @throws Exception If conversion fails
   */
  public Event convertEthLogToEventEntity(Log ethLog) throws Exception {
    // Create a new Web3j instance for this operation
    Web3j web3j = web3jConfig.createWebSocketWeb3j();

    try {
      // Get block information using the log's block number
      EthBlock ethBlock =
          web3j
              .ethGetBlockByNumber(DefaultBlockParameter.valueOf(ethLog.getBlockNumber()), false)
              .send();

      EthBlock.Block block = ethBlock.getBlock();

      // Get ABI event definition for the log
      org.web3j.abi.datatypes.Event abiEvent = abiParser.getABIEventByLog(ethLog);
      if (abiEvent == null) {
        logger.info("Event definition not found in ABI");
        throw new Exception("Event definition not found in ABI");
      }

      // Extract event parameters using Web3j's utilities
      EventValues eventValues = Contract.staticExtractEventParameters(abiEvent, ethLog);
      if (eventValues == null) {
        logger.info("No event values found for log: {}", ethLog);
        throw new Exception("No event values found for log");
      }

      // Process indexed parameters
      Map<String, Object> indexedValues = new HashMap<>();
      List<Type> indexedParameters = eventValues.getIndexedValues();
      List<TypeReference<Type>> indexedReferences = abiEvent.getIndexedParameters();

      for (int i = 0; i < indexedParameters.size(); i++) {
        String name = indexedReferences.get(i).getType().getTypeName();
        Object value = indexedParameters.get(i).getValue();
        indexedValues.put(name, value);
      }
      String indexedJson = objectMapper.writeValueAsString(indexedValues);

      // Process non-indexed parameters
      Map<String, Object> nonIndexedValues = new HashMap<>();
      List<Type> nonIndexedParameters = eventValues.getNonIndexedValues();
      List<TypeReference<Type>> nonIndexedReferences = abiEvent.getNonIndexedParameters();

      for (int i = 0; i < nonIndexedParameters.size(); i++) {
        String name = nonIndexedReferences.get(i).getType().getTypeName();
        Object value = nonIndexedParameters.get(i).getValue();
        nonIndexedValues.put(name, value);
      }
      String nonIndexedJson = objectMapper.writeValueAsString(nonIndexedValues);

      // Serialize log to JSON
      String logJson = objectMapper.writeValueAsString(ethLog);

      // Create and return new Event entity
      return Event.builder()
          .name(abiEvent.getName())
          .transactionHash(ethLog.getTransactionHash())
          .logIndex((int) ethLog.getLogIndex().longValue())
          .indexedValues(indexedJson)
          .nonIndexedValues(nonIndexedJson)
          .blockTimestamp(block.getTimestamp().longValue())
          .log(logJson)
          .build();
    } finally {
      // Shutdown the Web3j instance to free resources
      web3j.shutdown();
    }
  }

  /**
   * Retrieves the block timestamp for a given block number.
   *
   * @param blockNumber The block number to retrieve the timestamp for.
   * @return The block timestamp in seconds since epoch.
   * @throws IOException If there is an error communicating with the Ethereum node
   */
  private long getBlockTimestamp(BigInteger blockNumber) throws IOException {
    // Create a new Web3j instance for this operation
    Web3j web3j = web3jConfig.createWebSocketWeb3j();

    try {
      return web3j
          .ethGetBlockByNumber(DefaultBlockParameter.valueOf(blockNumber), false)
          .send()
          .getBlock()
          .getTimestamp()
          .longValue();
    } finally {
      // Shutdown the Web3j instance to free resources
      web3j.shutdown();
    }
  }

  /**
   * Get filtered logs from a specific block height
   *
   * @param blockHeight Block number to start from
   * @return Queue of transactions containing events
   */
  public BlockingQueue<Transaction> getPendingTransactions(long blockHeight) {
    return getPendingTransactions(blockHeight, false);
  }

  /**
   * Get filtered logs from a specific block height with an option to force an error in the outer
   * catch block This method is primarily used for testing the error handling in the outer catch
   * block
   *
   * @param blockHeight Block number to start from
   * @param forceOuterError Whether to force an error in the outer catch block (for testing)
   * @return Queue of transactions containing events
   */
  public BlockingQueue<Transaction> getPendingTransactions(
      long blockHeight, boolean forceOuterError) {
    try {
      // Create a new Web3j instance for this operation
      Web3j web3j = web3jConfig.createWebSocketWeb3j();

      try {
        // Create filter to get logs from the specified block height
        org.web3j.protocol.core.methods.request.EthFilter filter =
            new org.web3j.protocol.core.methods.request.EthFilter(
                DefaultBlockParameter.valueOf(BigInteger.valueOf(blockHeight)),
                DefaultBlockParameter.valueOf("latest"),
                Collections.emptyList());

        // Get logs synchronously
        List<EthLog.LogResult> filterLogs = web3j.ethGetLogs(filter).send().getLogs();

        logger.info("Retrieved {} logs from block height {}", filterLogs.size(), blockHeight);

        // Process logs and return the queue when done
        return CompletableFuture.supplyAsync(
                () -> {
                  BlockingQueue<Transaction> transactionQueue = new LinkedBlockingQueue<>();
                  if (forceOuterError) {
                    throw new RuntimeException("Forced error in outer catch block for testing");
                  }

                  for (EthLog.LogResult logResult : filterLogs) {
                    try {
                      org.web3j.protocol.core.methods.response.Log ethLog =
                          (org.web3j.protocol.core.methods.response.Log) logResult.get();
                      logger.info("Event found tx_hash={}", ethLog.getTransactionHash());

                      Event event = convertEthLogToEventEntity(ethLog);
                      logger.info(
                          "Event parsed tx_hash={}, name={}", event.transactionHash, event.name);

                      BlockHeight height =
                          BlockHeight.builder()
                              .blockNumber(ethLog.getBlockNumber().longValue())
                              .build();

                      Transaction transaction =
                          Transaction.builder()
                              .events(Collections.singletonList(event))
                              .blockHeight(height)
                              .build();

                      transactionQueue.add(transaction);
                    } catch (Exception e) {
                      logger.error("Error processing individual log", e);
                    }
                  }
                  return transactionQueue;
                })
            .get(30, java.util.concurrent.TimeUnit.SECONDS); // Wait up to 30 seconds
      } finally {
        // Shutdown the Web3j instance to free resources
        web3j.shutdown();
      }
    } catch (Exception e) {
      logger.error("Error getting filtered logs", e);
      return new LinkedBlockingQueue<>(); // Return empty queue on error
    }
  }
}
