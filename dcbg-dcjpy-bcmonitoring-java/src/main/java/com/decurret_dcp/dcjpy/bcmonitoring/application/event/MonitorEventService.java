package com.decurret_dcp.dcjpy.bcmonitoring.application.event;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightRepository;
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventRepository;
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepository;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.StructuredLogContext;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import org.springframework.stereotype.Service;

@Service
public class MonitorEventService {
  private final LoggingService log;
  private final EventLogRepository eventLogRepository;
  private final EventRepository eventRepository;
  private final BlockHeightRepository blockHeightRepository;
  private final ObjectMapper objectMapper;
  private final BcmonitoringConfigurationProperties properties;
  private final AtomicBoolean running = new AtomicBoolean(true);

  public MonitorEventService(
      LoggingService logger,
      EventLogRepository eventLogRepository,
      EventRepository eventRepository,
      BlockHeightRepository blockHeightRepository,
      BcmonitoringConfigurationProperties properties) {
    this.log = logger;
    this.eventLogRepository = eventLogRepository;
    this.eventRepository = eventRepository;
    this.blockHeightRepository = blockHeightRepository;
    this.properties = properties;
    this.objectMapper = new ObjectMapper();
  }

  /**
   * Execute the monitoring process This method will run in a loop, checking for new events and
   * processing them.
   *
   * @throws NumberFormatException if the check interval is not a valid integer
   * @throw Exception if there is an error during the monitoring process
   */
  public void execute() {
    int checkInterval;
    try {
      checkInterval = Integer.parseInt(properties.getSubscription().getCheckInterval());
    } catch (NumberFormatException e) {
      log.error("Failed to convert checkInterval: {}", e.getMessage());
      throw e;
    }

    while (running.get()) {
      try {
        monitorEvents(checkInterval);
      } catch (Exception e) {
        log.error("Error in monitoring loop: {}", e.getMessage(), e);
        sleep(checkInterval);
      }
    }
  }

  /**
   * Monitor events from the blockchain and process them. This method will subscribe to new events
   * and process pending transactions.
   *
   * @param checkInterval Interval to check for new transactions
   * @throw Exception if there is an error during the monitoring process
   */
  private void monitorEvents(int checkInterval) {
    // Get current block height
    long blockNumber;
    try {
      blockNumber = blockHeightRepository.get();
      log.info("Get blockheight: {}", blockNumber);
    } catch (Exception e) {
      log.error("Failed to get blockheight: {}", e.getMessage());
      throw e;
    }

    // Using virtual threads executor
    try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
      // Subscribe to new events
      BlockingQueue<Transaction> transactionsQueue = eventLogRepository.subscribe();

      // Get pending transactions
      BlockingQueue<Transaction> pendingTransactionsQueue =
          eventLogRepository.getFilterLogs(blockNumber + 1);

      // Process pending transactions first, then switch to monitoring
      executor.submit(
          () -> {
            BlockHeight finalBlockHeight =
                processPendingTransactions(pendingTransactionsQueue, checkInterval);
            if (finalBlockHeight != null && finalBlockHeight.blockNumber > 0) {
              // Process new transactions
              processNewTransactions(transactionsQueue, checkInterval);
            }
          });
    } catch (Exception e) {
      log.error("Error in monitoring: {}", e.getMessage());
    } finally {
      sleep(checkInterval);
    }
  }

  /**
   * Process pending transactions from the queue. This method will save the block height and events
   * to the database.
   *
   * @param pendingQueue BlockingQueue of pending transactions
   * @param checkInterval Interval to check for new transactions
   * @return BlockHeight object containing the last processed block height
   */
  private BlockHeight processPendingTransactions(
      BlockingQueue<Transaction> pendingQueue, int checkInterval) {
    BlockHeight exBlockHeight = BlockHeight.builder().blockNumber(0).build();

    while (true) {
      try {
        Transaction tx = pendingQueue.poll(1, TimeUnit.SECONDS);

        // Queue is empty, we processed all pending transactions
        if (tx == null) {
          // Save last block height before returning
          if (exBlockHeight.blockNumber > 0) {
            if (!savePendingTransactionBlockNumber(exBlockHeight)) {
              sleep(checkInterval);
              return null; // Error saving block height
            }
          }
          return exBlockHeight;
        }

        // Process block height change
        if (exBlockHeight.blockNumber != 0
            && exBlockHeight.blockNumber != tx.blockHeight.blockNumber) {
          if (!savePendingTransactionBlockNumber(exBlockHeight)) {
            sleep(checkInterval);
            return null; // Error saving block height
          }
        }

        if (tx.blockHeight.blockNumber == 0) {
          log.info("Pending block height Number is zero");
          sleep(checkInterval);
          return null; // Invalid block height
        }

        if (!savePendingTransaction(tx)) {
          sleep(checkInterval);
          return null; // Error saving transaction
        }

        exBlockHeight = tx.blockHeight;

      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        log.error("Interrupted while processing pending transactions: {}", e.getMessage());
        sleep(checkInterval);
        return null;
      }
    }
  }

  /**
   * Process new transactions from the queue. This method will save the events to the database.
   *
   * @param transactionsQueue BlockingQueue of new transactions
   * @param checkInterval Interval to check for new transactions
   */
  private void processNewTransactions(
      BlockingQueue<Transaction> transactionsQueue, int checkInterval) {
    if (transactionsQueue.isEmpty()) {
      return;
    }
    while (running.get()) {
      try {
        Transaction tx = transactionsQueue.take(); // Blocks until transaction is available

        if (tx.blockHeight.blockNumber == 0) {
          log.warn("Block height Number is zero");
          return;
        }

        if (!saveTransaction(tx)) {
          return;
        }

      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        log.error("Interrupted while processing transactions: {}", e.getMessage());
        return;
      }
    }
  }

  /**
   * Save transaction to the database.
   *
   * @param tx Transaction object containing the events and block height
   * @return true if the transaction was saved successfully, false otherwise
   */
  private boolean saveTransaction(Transaction tx) {
    // Process all events in the transaction
    for (Event e : tx.events) {
      if (e.transactionHash.isEmpty()) {
        log.error("Event transaction hash is zero");
        return false;
      }

      String traceId = fetchTraceId(e.nonIndexedValues);
      try (var logContext =
          StructuredLogContext.forBlockchainEvent(
              e.name,
              e.transactionHash,
              tx.blockHeight.blockNumber,
              e.logIndex,
              e.blockTimestamp,
              traceId)) {

        if (!eventRepository.save(e)) {
          log.error("Failure to register event");
          return false;
        }
        log.info("Success to register event");
      }
    }

    if (!blockHeightRepository.save(tx.blockHeight)) {
      log.error("Failure to register block number");
      return false;
    }
    log.info("Success to register block number");
    return true;
  }

  /**
   * Save pending transaction to the database.
   *
   * @param tx Transaction object containing the events and block height
   * @return true if the transaction was saved successfully, false otherwise
   */
  private boolean savePendingTransaction(Transaction tx) {
    for (Event e : tx.events) {
      if (e.transactionHash.isEmpty()) {
        log.error("Event transaction hash is zero");
        return false;
      }

      String traceId = fetchTraceId(e.nonIndexedValues);
      try (var logContext =
          StructuredLogContext.forBlockchainEvent(
              e.name,
              e.transactionHash,
              tx.blockHeight.blockNumber,
              e.logIndex,
              e.blockTimestamp,
              traceId)) {

        if (!eventRepository.save(e)) {
          log.error("Failure to register event");
          return false;
        }
        log.info("Success to register event");
      }
    }
    return true;
  }

  /**
   * Save pending transaction block number to the database.
   *
   * @param blockHeight BlockHeight object containing the block number
   * @return true if the block height was saved successfully, false otherwise
   */
  private boolean savePendingTransactionBlockNumber(BlockHeight blockHeight) {
    if (!blockHeightRepository.save(blockHeight)) {
      log.error("Failure to register block number: {}", blockHeight.blockNumber);
      return false;
    }
    log.info("Success to register block number: {}", blockHeight.blockNumber);
    return true;
  }

  private static class ParsedTraceId {
    public byte[] traceId;
  }

  /**
   * Fetch trace ID from non-indexed values.
   *
   * @param nonIndexedValues Non-indexed values as a JSON string
   * @return Trace ID as a string
   */
  private String fetchTraceId(String nonIndexedValues) {
    try {
      ParsedTraceId parsed = objectMapper.readValue(nonIndexedValues, ParsedTraceId.class);
      if (parsed.traceId == null || parsed.traceId.length == 0) {
        return "";
      }

      StringBuilder sb = new StringBuilder();
      for (byte b : parsed.traceId) {
        if (b != 0) {
          sb.append((char) b);
        }
      }
      return sb.toString();
    } catch (JsonProcessingException e) {
      log.error("Error parsing trace ID: {}", e.getMessage());
      return "";
    }
  }

  /**
   * Sleep for a specified number of milliseconds.
   *
   * @param milliseconds Number of milliseconds to sleep
   */
  private void sleep(int milliseconds) {
    try {
      Thread.sleep(milliseconds);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }
  }
}
