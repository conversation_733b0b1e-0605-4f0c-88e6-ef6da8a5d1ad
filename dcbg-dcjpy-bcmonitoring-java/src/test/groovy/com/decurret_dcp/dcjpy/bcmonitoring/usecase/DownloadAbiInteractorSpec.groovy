package com.decurret_dcp.dcjpy.bcmonitoring.usecase

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3AbiRepository
import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ContractInfo
import com.decurret_dcp.dcjpy.bcmonitoring.exception.ConfigurationException
import com.decurret_dcp.dcjpy.bcmonitoring.exception.S3Exception
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import java.time.Instant
import org.slf4j.Logger
import software.amazon.awssdk.services.s3.model.CommonPrefix
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response
import software.amazon.awssdk.services.s3.model.S3Object
import spock.lang.Specification

class DownloadAbiInteractorSpec extends Specification {

	LoggingService logger
	S3AbiRepository s3AbiRepository
	AbiParser abiParser
	BcmonitoringConfigurationProperties properties
	BcmonitoringConfigurationProperties.Aws aws
	BcmonitoringConfigurationProperties.Aws.S3 s3Props
	DownloadAbiService interactor

	def setup() {
		logger  = Mock(LoggingService)
		s3AbiRepository = Mock(S3AbiRepository)
		abiParser = Mock(AbiParser)
		properties = Mock(BcmonitoringConfigurationProperties)
		aws = Mock(BcmonitoringConfigurationProperties.Aws)
		s3Props = Mock(BcmonitoringConfigurationProperties.Aws.S3)

		properties.getAws() >> aws
		aws.getS3() >> s3Props
		s3Props.getBucketName() >> "test-bucket"

		interactor = new DownloadAbiService(logger, s3AbiRepository, abiParser, properties)
	}

	def "should throw ConfigurationException when S3 bucket name is empty"() {
		given:
		// Create new mocks for this test
		def testLogger = Mock(LoggingService)
		def testS3AbiRepository = Mock(S3AbiRepository)
		def testAbiParser = Mock(AbiParser)
		def testProperties = Mock(BcmonitoringConfigurationProperties)
		def testAws = Mock(BcmonitoringConfigurationProperties.Aws)
		def testS3Props = Mock(BcmonitoringConfigurationProperties.Aws.S3)

		// Set up the mocks
		testProperties.getAws() >> testAws
		testAws.getS3() >> testS3Props
		testS3Props.getBucketName() >> ""

		// Create a new service instance with the mocks
		def testService = new DownloadAbiService(testLogger, testS3AbiRepository, testAbiParser, testProperties)

		when:
		testService.execute()

		then:
		1 * testLogger.info("downloading abi files... bucket_name={}", "")
		1 * testLogger.error("S3 bucket name is not configured")
		def exception = thrown(ConfigurationException)
		exception.message == "S3 bucket name is not configured"
	}

	def "should throw ConfigurationException when S3 bucket name is null"() {
		given:
		// Create new mocks for this test
		def testLogger = Mock(LoggingService)
		def testS3AbiRepository = Mock(S3AbiRepository)
		def testAbiParser = Mock(AbiParser)
		def testProperties = Mock(BcmonitoringConfigurationProperties)
		def testAws = Mock(BcmonitoringConfigurationProperties.Aws)
		def testS3Props = Mock(BcmonitoringConfigurationProperties.Aws.S3)

		// Set up the mocks
		testProperties.getAws() >> testAws
		testAws.getS3() >> testS3Props
		testS3Props.getBucketName() >> null

		// Create a new service instance with the mocks
		def testService = new DownloadAbiService(testLogger, testS3AbiRepository, testAbiParser, testProperties)

		when:
		testService.execute()

		then:
		1 * testLogger.info("downloading abi files... bucket_name={}", null)
		1 * testLogger.error("S3 bucket name is not configured")
		def exception = thrown(ConfigurationException)
		exception.message == "S3 bucket name is not configured"
	}

	def "should process multiple prefixes, objects and download json files"() {
		given:
		def commonPrefixes = [
			CommonPrefix.builder().prefix("3000/").build(),
			CommonPrefix.builder().prefix("3001/").build(),
			CommonPrefix.builder().prefix("3002/").build()
		]
		def listPrefixesResponse = ListObjectsV2Response.builder()
				.commonPrefixes(commonPrefixes)
				.build()

		def s3Objects = [
			S3Object.builder().key("3000/test1.json").lastModified(Instant.now()).build(),
			S3Object.builder().key("3001/test2.json").lastModified(Instant.now()).build(),
			S3Object.builder().key("3002/test3.json").lastModified(Instant.now()).build()
		]
		def listObjectsResponse = ListObjectsV2Response.builder()
				.contents(s3Objects)
				.build()

		def inputStream = Mock(InputStream)
		def contractInfo = ContractInfo.builder().build()

		when:
		interactor.execute()

		then:
		1 * s3AbiRepository.listCommonPrefixesObjects("test-bucket", DCFConst.SLASH) >> commonPrefixes
		3 * s3AbiRepository.listObjects("test-bucket", _) >> listObjectsResponse
		9 * s3AbiRepository.getObject("test-bucket", _) >> inputStream
		9 * abiParser.parseAbiContent(_, _, _) >> contractInfo
		0 * logger.error(_, _)
	}

	def "should process single prefix, objects and download json files"() {
		given:
		def commonPrefixes = [
			CommonPrefix.builder().prefix("3000/").build()
		]
		def listPrefixesResponse = ListObjectsV2Response.builder()
				.commonPrefixes(commonPrefixes)
				.build()

		def s3Objects = [
			S3Object.builder().key("3000/test1.json").lastModified(Instant.now()).build()
		]
		def listObjectsResponse = ListObjectsV2Response.builder()
				.contents(s3Objects)
				.build()

		def inputStream = Mock(InputStream)
		def contractInfo = ContractInfo.builder().build()

		when:
		interactor.execute()

		then:
		1 * s3AbiRepository.listCommonPrefixesObjects("test-bucket", DCFConst.SLASH) >> commonPrefixes
		1 * s3AbiRepository.listObjects("test-bucket", _) >> listObjectsResponse
		1 * s3AbiRepository.getObject("test-bucket", _) >> inputStream
		1 * abiParser.parseAbiContent(_, _, _) >> contractInfo
		0 * logger.error(_, _)
	}

	def "should skip non-json files"() {
		given:
		def commonPrefixes = [
			CommonPrefix.builder().prefix("3000/").build(),
			CommonPrefix.builder().prefix("3001/").build(),
			CommonPrefix.builder().prefix("3002/").build()
		]

		def s3Objects = [
			S3Object.builder().key("3000/test1.txt").lastModified(Instant.now()).build(),
			S3Object.builder().key("3001/test2.yml").lastModified(Instant.now()).build(),
			S3Object.builder().key("3002/test3.md").lastModified(Instant.now()).build()
		]
		def listObjectsResponse = ListObjectsV2Response.builder()
				.contents(s3Objects)
				.build()

		when:
		interactor.execute()

		then:
		1 * s3AbiRepository.listCommonPrefixesObjects("test-bucket", DCFConst.SLASH) >> commonPrefixes
		3 * s3AbiRepository.listObjects("test-bucket", _) >> listObjectsResponse
		0 * s3AbiRepository.getObject(_, _)
		0 * abiParser.parseAbiContent(_, _, _)
		9 * logger.info("This object will be skipped because the extension is not .json: {}", _)
	}

	def "should handle empty object list"() {
		given:
		def commonPrefixes = [
			CommonPrefix.builder().prefix("3000/").build(),
			CommonPrefix.builder().prefix("3001/").build()
		]

		def emptyListObjectsResponse = ListObjectsV2Response.builder()
				.contents([])
				.build()

		when:
		interactor.execute()

		then:
		1 * s3AbiRepository.listCommonPrefixesObjects("test-bucket", DCFConst.SLASH) >> commonPrefixes
		2 * s3AbiRepository.listObjects("test-bucket", _) >> emptyListObjectsResponse
		0 * s3AbiRepository.getObject(_, _)
		0 * abiParser.parseAbiContent(_, _, _)
	}

	def "should throw S3Exception when listing prefixes fails"() {
		when:
		interactor.execute()

		then:
		1 * s3AbiRepository.listCommonPrefixesObjects("test-bucket", DCFConst.SLASH) >> null
		1 * logger.error("Failed to list S3 CommonPrefixes objects")
		def e = thrown(S3Exception)
		e.message == "Failed to list S3 CommonPrefixes objects"
	}

	def "should throw S3Exception when listing objects fails"() {
		given:
		def commonPrefixes = [
			CommonPrefix.builder().prefix("3000/").build()
		]

		when:
		interactor.execute()

		then:
		1 * s3AbiRepository.listCommonPrefixesObjects("test-bucket", DCFConst.SLASH) >> commonPrefixes
		1 * s3AbiRepository.listObjects("test-bucket", _) >> null
		1 * logger.error(_ as String)
		def e = thrown(S3Exception)
		e.message.contains("Failed to list S3 objects with prefix")
	}

	def "should only process direct child objects"() {
		given:
		def commonPrefixes = [
			CommonPrefix.builder().prefix("3000/").build()
		]

		def s3Objects = [
			S3Object.builder().key("3000/direct.json").lastModified(Instant.now()).build(),
			S3Object.builder().key("3000/nested/file.json").lastModified(Instant.now()).build(),
			S3Object.builder().key("3000/deeply/nested/file.json").lastModified(Instant.now()).build()
		]
		def listObjectsResponse = ListObjectsV2Response.builder()
				.contents(s3Objects)
				.build()

		def inputStream = Mock(InputStream)
		def contractInfo = ContractInfo.builder().build()

		when:
		interactor.execute()

		then:
		1 * s3AbiRepository.listCommonPrefixesObjects("test-bucket", DCFConst.SLASH) >> commonPrefixes
		1 * s3AbiRepository.listObjects("test-bucket", _) >> listObjectsResponse
		1 * s3AbiRepository.getObject("test-bucket", "3000/direct.json") >> inputStream
		1 * abiParser.parseAbiContent(_, _, _) >> contractInfo
		0 * s3AbiRepository.getObject("test-bucket", "3000/nested/file.json")
		0 * s3AbiRepository.getObject("test-bucket", "3000/deeply/nested/file.json")
	}

	def "should throw S3Exception when object retrieval fails"() {
		given:
		def commonPrefixes = [
			CommonPrefix.builder().prefix("3000/").build()
		]

		def s3Objects = [
			S3Object.builder().key("3000/test.json").lastModified(Instant.now()).build()
		]
		def listObjectsResponse = ListObjectsV2Response.builder()
				.contents(s3Objects)
				.build()

		when:
		interactor.execute()

		then:
		1 * s3AbiRepository.listCommonPrefixesObjects("test-bucket", DCFConst.SLASH) >> commonPrefixes
		1 * s3AbiRepository.listObjects("test-bucket", _) >> listObjectsResponse
		1 * s3AbiRepository.getObject("test-bucket", "3000/test.json") >> null
		1 * logger.error("Failed to get S3 abi object: 3000/test.json")
		def e = thrown(S3Exception)
		e.message == "Failed to get S3 abi object: 3000/test.json"
	}

	def "should throw IOException when parsing fails"() {
		given:
		def commonPrefixes = [
			CommonPrefix.builder().prefix("3000/").build()
		]

		def s3Objects = [
			S3Object.builder().key("3000/test.json").lastModified(Instant.now()).build()
		]
		def listObjectsResponse = ListObjectsV2Response.builder()
				.contents(s3Objects)
				.build()

		def inputStream = Mock(InputStream)
		def parseException = new IOException("Parse error")

		when:
		interactor.execute()

		then:
		1 * s3AbiRepository.listCommonPrefixesObjects("test-bucket", DCFConst.SLASH) >> commonPrefixes
		1 * s3AbiRepository.listObjects("test-bucket", _) >> listObjectsResponse
		1 * s3AbiRepository.getObject("test-bucket", "3000/test.json") >> inputStream
		1 * abiParser.parseAbiContent(_, _, _) >> { throw parseException }
		1 * logger.error("Failed to parse S3 abi object: 3000/test.json", parseException)
		def e = thrown(IOException)
		e.message == "Failed to parse S3 abi object: 3000/test.json"
		e.cause == parseException
	}

	def "should process single prefix, name start with dot"() {
		given:
		def commonPrefixes = [
			CommonPrefix.builder().prefix("3000/").build()
		]
		def listPrefixesResponse = ListObjectsV2Response.builder()
				.commonPrefixes(commonPrefixes)
				.build()

		def s3Objects = [
			S3Object.builder().key("3000/.json").lastModified(Instant.now()).build()
		]
		def listObjectsResponse = ListObjectsV2Response.builder()
				.contents(s3Objects)
				.build()

		def inputStream = Mock(InputStream)
		def contractInfo = ContractInfo.builder().build()

		when:
		interactor.execute()

		then:
		1 * s3AbiRepository.listCommonPrefixesObjects("test-bucket", DCFConst.SLASH) >> commonPrefixes
		1 * s3AbiRepository.listObjects("test-bucket", _) >> listObjectsResponse
		0 * logger.error(_, _)
		0 * logger.error(_)
	}
}
