package com.decurret_dcp.dcjpy.bcmonitoring.usecase

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightRepository
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventRepository
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepository
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import java.util.concurrent.BlockingQueue
import java.util.concurrent.ExecutorService
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import spock.lang.Specification
import spock.lang.Subject

class MonitorEventServiceSpec extends Specification {

	LoggingService mockLogger = Mock()
	EventLogRepository mockEventLogRepo = Mock()
	EventRepository mockEventRepo = Mock()
	BlockHeightRepository mockBlockHeightRepo = Mock()
	BcmonitoringConfigurationProperties mockProperties = Mock()
	BcmonitoringConfigurationProperties.Subscription mockSubscription = Mock()

	@Subject
	MonitorEventService interactor

	def setup() {
		mockProperties.getSubscription() >> mockSubscription
		interactor = new MonitorEventService(
				mockLogger,
				mockEventLogRepo,
				mockEventRepo,
				mockBlockHeightRepo,
				mockProperties
				)
	}

	def "execute should process one iteration and terminate when running is set to false"() {
		given:
		mockSubscription.getCheckInterval() >> "100"
		def runningField = MonitorEventService.class.getDeclaredField("running")
		runningField.setAccessible(true)
		AtomicBoolean running = runningField.get(interactor)

		// Mock dependencies for successful execution
		def blockHeight = 1000L
		def txQueue = new LinkedBlockingQueue<Transaction>()
		def pendingQueue = new LinkedBlockingQueue<Transaction>()

		when:
		// Run in separate thread so we can control it
		Thread testThread = new Thread({
			interactor.execute()
		})
		testThread.start()

		// Give time for the first iteration
		Thread.sleep(100)

		// Stop the loop
		running.set(false)
		testThread.join(2000) // Wait for thread to finish with timeout

		then:
		1 * mockBlockHeightRepo.get() >> blockHeight
		1 * mockEventLogRepo.subscribe() >> txQueue
		1 * mockEventLogRepo.getFilterLogs(blockHeight + 1) >> pendingQueue
		!testThread.isAlive() // Thread should have terminated
	}

	def "execute should catch NumberFormatException and log error when checkInterval is invalid"() {
		given:
		// Set invalid check interval
		mockSubscription.getCheckInterval() >> "invalid"

		when:
		Throwable thrown = null
		try {
			interactor.execute()
		} catch (NumberFormatException e) {
			thrown = e
		}

		then:
		thrown instanceof NumberFormatException
		1 * mockLogger.error("Failed to convert checkInterval: {}", _ as String) // Match any string
	}

	def "execute should handle exceptions in monitoring loop"() {
		given:
		mockSubscription.getCheckInterval() >> "100"
		// Set running to false after first iteration
		setRunning(false)
		mockBlockHeightRepo.get() >> { throw new RuntimeException("Test exception") }

		when:
		interactor.execute()

		then:
		noExceptionThrown()
	}

	def "execute should catch Exception in monitoring loop, log error, and continue execution"() {
		given:
		// Set valid check interval
		mockSubscription.getCheckInterval() >> "100"

		// Access the private running field to control the loop
		def runningField = MonitorEventService.class.getDeclaredField("running")
		runningField.setAccessible(true)
		AtomicBoolean running = runningField.get(interactor)

		// Mock blockHeightRepo to always throw exception
		mockBlockHeightRepo.get() >> { throw new RuntimeException("Test exception in monitoring") }

		when:
		// Run in separate thread so we can control it
		Thread testThread = new Thread({
			interactor.execute()
		})
		testThread.start()

		// Give time for a few iterations
		Thread.sleep(300) // Should allow for ~3 iterations with 100ms interval

		// Stop the loop
		running.set(false)
		testThread.join(1000) // Wait for thread to finish with timeout

		then:
		(2.._) * mockLogger.error("Error in monitoring loop: {}", _ as String, _ as Exception)
		!testThread.isAlive() // Thread should have terminated
	}

	def "monitorEvents should process block height and transactions"() {
		given:
		mockSubscription.getCheckInterval() >> "100"
		setRunning(false)
		def blockNumber = 100L
		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		def transaction = createTransaction(blockNumber + 1)
		pendingQueue.add(transaction)

		when:
		interactor.execute()

		then:
		noExceptionThrown()
	}

	def "monitorEvents should handle exceptions"() {
		given:
		mockSubscription.getCheckInterval() >> "100"
		setRunning(false)
		mockBlockHeightRepo.get() >> 100L
		mockEventLogRepo.subscribe() >> { throw new RuntimeException("Test exception") }

		when:
		interactor.execute()

		then:
		noExceptionThrown()
	}

	def "monitorEvents should catch Exception, log error, and continue execution"() {
		given:
		// Set valid check interval
		mockSubscription.getCheckInterval() >> "100"

		// Access the private running field to control the loop
		def runningField = MonitorEventService.class.getDeclaredField("running")
		runningField.setAccessible(true)
		AtomicBoolean running = runningField.get(interactor)

		// Setup mockBlockHeightRepo to return value first time
		mockBlockHeightRepo.get() >>> [
			100L,
			{
				throw new RuntimeException("Test exception in monitorEvents")
			}
		]

		// Mock subscription to return an empty queue
		def txQueue = new LinkedBlockingQueue<Transaction>()
		mockEventLogRepo.subscribe() >> txQueue

		when:
		// Run in separate thread so we can control it
		Thread testThread = new Thread({
			interactor.execute()
		})
		testThread.start()

		// Give time for a few iterations
		Thread.sleep(300) // Should allow for ~3 iterations with 100ms interval

		// Stop the loop
		running.set(false)
		testThread.join(1000) // Wait for thread to finish with timeout

		then:
		!testThread.isAlive() // Thread should have terminated
		noExceptionThrown()
	}

	def "monitorEvents should process new transactions when finalBlockHeight is valid"() {
		given:
		// Set valid check interval
		mockSubscription.getCheckInterval() >> "100"

		// Mock the block height repository to return a valid block height
		mockBlockHeightRepo.get() >> 100L

		// Create mock queues
		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		def transactionsQueue = Mock(BlockingQueue)

		// Add a valid transaction to the pending queue
		def tx = createTransaction(101L)
		pendingQueue.add(tx)

		// Setup mocks for queue behavior
		mockEventLogRepo.getFilterLogs(101L) >> pendingQueue
		mockEventLogRepo.subscribe() >> transactionsQueue

		// Configure transaction queue to throw InterruptedException after one take()
		// to exit the processNewTransactions loop
		transactionsQueue.take() >> { throw new InterruptedException("Test interrupt") }

		// Access and set running field to stop the outer loop after one iteration
		setRunning(false)

		when:
		interactor.execute()

		then:
		noExceptionThrown()
	}

	def "processPendingTransactions should process transactions and update block height"() {
		given:
		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		def tx = createTransaction(101L)
		pendingQueue.add(tx)

		when:
		def result = callProcessPendingTransactions(pendingQueue, 100)

		then:
		1 * mockEventRepo.save(_) >> true
		1 * mockBlockHeightRepo.save(_) >> true
		1 * mockLogger.info("Success to register event")
		1 * mockLogger.info("Success to register block number: {}", 101L)
		result != null
		result.blockNumber == 101L
	}

	def "processPendingTransactions should handle empty queue"() {
		given:
		def pendingQueue = new LinkedBlockingQueue<Transaction>()

		when:
		def result = callProcessPendingTransactions(pendingQueue, 100)

		then:
		result != null
		result.blockNumber == 0
		0 * mockBlockHeightRepo.save(_)
	}

	def "processPendingTransactions should handle block height change"() {
		given:
		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		def tx1 = createTransaction(101L)
		def tx2 = createTransaction(102L)
		pendingQueue.add(tx1)
		pendingQueue.add(tx2)

		when:
		def result = callProcessPendingTransactions(pendingQueue, 100)

		then:
		2 * mockEventRepo.save(_) >> true
		2 * mockBlockHeightRepo.save(_) >> true
		result != null
		result.blockNumber == 102L
	}

	def "processPendingTransactions should handle zero block height"() {
		given:
		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		def tx = createTransaction(0L)
		pendingQueue.add(tx)

		when:
		def result = callProcessPendingTransactions(pendingQueue, 100)

		then:
		1 * mockLogger.info("Pending block height Number is zero")
		result == null
	}

	def "processPendingTransactions should handle save failures"() {
		given:
		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		def tx = createTransaction(101L)
		pendingQueue.add(tx)

		when:
		def result = callProcessPendingTransactions(pendingQueue, 100)

		then:
		1 * mockEventRepo.save(_) >> false
		1 * mockLogger.error("Failure to register event")
		result == null
	}

	def "processPendingTransactions should handle interrupted exception"() {
		given:
		def pendingQueue = Mock(BlockingQueue)
		pendingQueue.poll(1, TimeUnit.SECONDS) >> { throw new InterruptedException() }

		when:
		def result = callProcessPendingTransactions(pendingQueue, 100)

		then:
		1 * mockLogger.error("Interrupted while processing pending transactions: {}", _)
		result == null
	}

	def "processPendingTransactions should handle block height save failure at end of queue"() {
		given:
		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		def tx = createTransaction(101L)
		pendingQueue.add(tx)

		// Configure eventRepository to succeed but blockHeightRepository to fail
		mockEventRepo.save(_) >> true
		mockBlockHeightRepo.save(_) >> false

		when:
		def result = callProcessPendingTransactions(pendingQueue, 100)

		then:
		1 * mockEventRepo.save(_) >> true
		1 * mockLogger.info("Success to register event")
		1 * mockLogger.error("Failure to register block number: {}", 101L)
		result == null
	}

	def "processPendingTransactions should handle block height save failure on block change"() {
		given:
		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		def tx1 = createTransaction(101L)
		def tx2 = createTransaction(102L) // Different block height
		pendingQueue.add(tx1)
		pendingQueue.add(tx2)

		// Configure event save to succeed but block height save to fail
		mockEventRepo.save(_) >> true

		when:
		def result = callProcessPendingTransactions(pendingQueue, 100)

		then:
		1 * mockEventRepo.save(_) >> true
		1 * mockLogger.info("Success to register event")
		1 * mockBlockHeightRepo.save({ it.blockNumber == 101L }) >> false
		1 * mockLogger.error("Failure to register block number: {}", 101L)
		result == null
	}

	def "processPendingTransactions should not save block height when consecutive transactions have same block height"() {
		given:
		def pendingQueue = new LinkedBlockingQueue<Transaction>()

		// Create 3 transactions with the same block height (101)
		def tx1 = createTransaction(101L)
		def tx2 = createTransaction(101L)
		def tx3 = createTransaction(101L)

		// Add all transactions to the queue
		pendingQueue.add(tx1)
		pendingQueue.add(tx2)
		pendingQueue.add(tx3)

		// Configure mocks to succeed
		mockEventRepo.save(_) >> true

		when:
		def result = callProcessPendingTransactions(pendingQueue, 100)

		then:
		// Should save all 3 events
		3 * mockEventRepo.save(_) >> true
		3 * mockLogger.info("Success to register event")

		// Should save block height only ONCE at the end when queue is empty
		1 * mockBlockHeightRepo.save({ it.blockNumber == 101L }) >> true
		1 * mockLogger.info("Success to register block number: {}", 101L)

		// Verify result
		result != null
		result.blockNumber == 101L
	}

	def "saveTransaction should handle empty transaction hash"() {
		given:
		// Create transaction with empty hash directly rather than trying to modify it
		def tx = createTransactionWithEmptyHash(101L)

		when:
		def result = callSaveTransaction(tx)

		then:
		1 * mockLogger.error("Event transaction hash is zero")
		!result
	}

	def "saveTransaction should save events and block height"() {
		given:
		def tx = createTransaction(101L)

		when:
		def result = callSaveTransaction(tx)

		then:
		1 * mockEventRepo.save(_) >> true
		1 * mockLogger.info("Success to register event")
		1 * mockBlockHeightRepo.save(_) >> true
		1 * mockLogger.info("Success to register block number")
		result
	}

	def "saveTransaction should handle event save failure"() {
		given:
		def tx = createTransaction(101L)

		when:
		def result = callSaveTransaction(tx)

		then:
		1 * mockEventRepo.save(_) >> false
		1 * mockLogger.error("Failure to register event")
		!result
	}

	def "saveTransaction should handle block height save failure"() {
		given:
		def tx = createTransaction(101L)

		when:
		def result = callSaveTransaction(tx)

		then:
		1 * mockEventRepo.save(_) >> true
		1 * mockBlockHeightRepo.save(_) >> false
		1 * mockLogger.error("Failure to register block number")
		!result
	}

	def "fetchTraceId should handle valid JSON"() {
		given:
		def nonIndexedValues = '{"traceId":[84,101,115,116,73,68]}'

		when:
		def result = callFetchTraceId(nonIndexedValues)

		then:
		result == "TestID"
	}

	def "fetchTraceId should handle empty traceId"() {
		given:
		def nonIndexedValues = '{"traceId":[]}'

		when:
		def result = callFetchTraceId(nonIndexedValues)

		then:
		result == ""
	}

	def "fetchTraceId should handle null traceId"() {
		given:
		def nonIndexedValues = '{"traceId":null}'

		when:
		def result = callFetchTraceId(nonIndexedValues)

		then:
		result == ""
	}

	def "fetchTraceId should handle JSON parsing exception"() {
		given:
		def nonIndexedValues = 'invalid json'

		when:
		def result = callFetchTraceId(nonIndexedValues)

		then:
		1 * mockLogger.error("Error parsing trace ID: {}", _)
		result == ""
	}

	def "fetchTraceId should skip zero bytes when building trace ID string"() {
		given:
		// Create JSON with traceId that contains zero bytes
		// This represents a byte array with: T e s t \0 I D \0
		def nonIndexedValues = '{"traceId":[84,101,115,116,0,73,68,0]}'

		when:
		def result = callFetchTraceId(nonIndexedValues)

		then:
		result == "TestID" // Zero bytes should be skipped
	}

	def "processNewTransactions should process transactions successfully"() {
		given:
		def transactionsQueue = new LinkedBlockingQueue<Transaction>()
		def tx = createTransaction(101L)
		transactionsQueue.add(tx)

		// Set running to false after first iteration to exit the loop
		setRunning(false)

		when:
		callProcessNewTransactions(transactionsQueue, 100)

		then:
		noExceptionThrown()
	}

	def "processNewTransactions should process transactions is empty"() {
		given:
		def transactionsQueue = new LinkedBlockingQueue<Transaction>()

		// Set running to false after first iteration to exit the loop
		setRunning(false)

		when:
		callProcessNewTransactions(transactionsQueue, 100)

		then:
		noExceptionThrown()
	}

	def "processNewTransactions should exit when block height is zero"() {
		given:
		def transactionsQueue = new LinkedBlockingQueue<Transaction>()
		def tx = createTransaction(0L)  // Block height is zero
		transactionsQueue.add(tx)

		// Set running to true, method should return early
		setRunning(true)

		when:
		callProcessNewTransactions(transactionsQueue, 100)

		then:
		1 * mockLogger.warn("Block height Number is zero")
		0 * mockEventRepo.save(_)
		0 * mockBlockHeightRepo.save(_)
		noExceptionThrown()
	}

	def "processNewTransactions should exit when saveTransaction returns false"() {
		given:
		def transactionsQueue = new LinkedBlockingQueue<Transaction>()
		def tx = createTransaction(101L)
		transactionsQueue.add(tx)

		// Set running to true, method should return early
		setRunning(true)

		// Mock saveTransaction to return false by making event save fail
		mockEventRepo.save(_) >> false

		when:
		callProcessNewTransactions(transactionsQueue, 100)

		then:
		1 * mockEventRepo.save(_) >> false
		1 * mockLogger.error("Failure to register event")
		0 * mockBlockHeightRepo.save(_)
		noExceptionThrown()
	}

	def "processNewTransactions should handle InterruptedException"() {
		given:
		def transactionsQueue = Mock(BlockingQueue)
		transactionsQueue.take() >> { throw new InterruptedException("Test interrupt") }

		// Set running to true
		setRunning(true)

		when:
		callProcessNewTransactions(transactionsQueue, 100)

		then:
		1 * mockLogger.error("Interrupted while processing transactions: {}", "Test interrupt")
		0 * mockEventRepo.save(_)
		0 * mockBlockHeightRepo.save(_)
		noExceptionThrown()
	}

	def "processNewTransactions should exit loop when running is false"() {
		given:
		def transactionsQueue = Mock(BlockingQueue)

		// Set running to false immediately
		setRunning(false)

		when:
		callProcessNewTransactions(transactionsQueue, 100)

		then:
		0 * transactionsQueue.take()
		0 * mockEventRepo.save(_)
		0 * mockBlockHeightRepo.save(_)
		noExceptionThrown()
	}

	def "processNewTransactions should exit early when saveTransaction returns false due to block height save failure"() {
		given:
		def transactionsQueue = new LinkedBlockingQueue<Transaction>()

		// Add two transactions to the queue
		def tx1 = createTransaction(101L)
		def tx2 = createTransaction(102L)
		transactionsQueue.add(tx1)
		transactionsQueue.add(tx2)

		// Set running to true for the test
		setRunning(true)

		// Make event save succeed but block height save fail
		mockEventRepo.save(_) >> true
		mockBlockHeightRepo.save(_) >> false

		when:
		callProcessNewTransactions(transactionsQueue, 100)

		then:
		// First transaction's event should be saved
		1 * mockEventRepo.save({ it.transactionHash == tx1.events[0].transactionHash }) >> true
		1 * mockLogger.info("Success to register event")

		// Block height save should fail
		1 * mockBlockHeightRepo.save({ it.blockNumber == 101L }) >> false
		1 * mockLogger.error("Failure to register block number")

		// Second transaction should not be processed at all
		0 * mockEventRepo.save({ it.transactionHash == tx2.events[0].transactionHash })

		// Queue should still contain the second transaction
		transactionsQueue.size() == 1
		transactionsQueue.peek().blockHeight.blockNumber == 102L

		noExceptionThrown()
	}

	def "savePendingTransaction should handle empty transaction hash"() {
		given:
		// Create transaction with empty hash
		def tx = createTransactionWithEmptyHash(101L)

		when:
		def result = callSavePendingTransaction(tx)

		then:
		1 * mockLogger.error("Event transaction hash is zero")
		0 * mockEventRepo.save(_) // Verify no save attempts were made
		!result // Should return false
	}

	def "savePendingTransactionBlockNumber should handle block height save failure"() {
		given:
		def blockHeight = BlockHeight.builder().blockNumber(101L).build()
		mockBlockHeightRepo.save(blockHeight) >> false

		when:
		def result = callSavePendingTransactionBlockNumber(blockHeight)

		then:
		1 * mockLogger.error("Failure to register block number: {}", 101L)
		!result // Should return false
	}

	def "monitorEvents should execute processNewTransactions when processPendingTransactions returns valid block height"() {
		given:
		// Set up valid block height
		def blockHeight = 100L
		mockBlockHeightRepo.get() >> blockHeight

		// Set up pending queue with test data
		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		def pendingTx = createTransaction(blockHeight + 1) // Block 101
		pendingQueue.add(pendingTx)

		// Create new tx for the mock queue to return
		def newTx = createTransaction(blockHeight + 2) // Block 102

		// Mock the BlockingQueue interface directly instead of spying on LinkedBlockingQueue
		def transactionsQueue = Mock(BlockingQueue)
		transactionsQueue.take() >>> [
			newTx,
			{
				throw new InterruptedException("Test complete")
			}
		]

		// Mock repository behavior
		mockEventLogRepo.getFilterLogs(blockHeight + 1) >> pendingQueue
		mockEventLogRepo.subscribe() >> transactionsQueue
		mockEventRepo.save(_) >> true
		mockBlockHeightRepo.save(_) >> true

		// Create executor mock that executes submitted tasks immediately
		def executor = Mock(java.util.concurrent.ExecutorService) {
			submit(_) >> { Runnable task ->
				task.run()
				return null
			}
			close() >> {}
		}

		when:
		// Replace executor creation with our mock
		MonitorEventService.metaClass.static.newExecutor = { -> executor }
		callMonitorEvents(100)

		then:
		// Verify pending transaction was processed
		1 * mockEventRepo.save({ it.transactionHash == pendingTx.events[0].transactionHash }) >> true
		1 * mockBlockHeightRepo.save({ it.blockNumber == 101L }) >> true
		1 * mockLogger.info("Success to register block number: {}", 101L)

		// Verify new transaction was processed
		1 * mockEventRepo.save({ it.transactionHash == newTx.events[0].transactionHash }) >> true
		1 * mockLogger.info("Success to register block number")

		// Verify the interrupt was caught
		//1 * mockLogger.error("Interrupted while processing transactions: {}", "Test complete")

		cleanup:
		MonitorEventService.metaClass = null
	}

	def "monitorEvents should log error when exception occurs in monitoring process"() {
		given:
		// Set up block height repo to return a valid number
		mockBlockHeightRepo.get() >> 100L

		// Set up event log repo to throw an exception when subscribe is called
		mockEventLogRepo.subscribe() >> { throw new RuntimeException("Test monitoring error") }

		when:
		callMonitorEvents(100)

		then:
		// Verify the error is logged with the correct message
		1 * mockLogger.error("Error in monitoring: {}", "Test monitoring error")

		// Ensure other methods aren't called after the exception
		0 * mockEventRepo.save(_)
		0 * mockBlockHeightRepo.save(_)

		// Sleep should still be called in the finally block
		noExceptionThrown()
	}

	def "monitorEvents should log error when exception escapes from executor task"() {
		given:
		// Set up valid block height
		def blockHeight = 100L
		mockBlockHeightRepo.get() >> blockHeight

		// Set up queues with test data
		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		def transactionsQueue = new LinkedBlockingQueue<Transaction>()

		// Configure repository behavior
		mockEventLogRepo.getFilterLogs(blockHeight + 1) >> pendingQueue
		mockEventLogRepo.subscribe() >> transactionsQueue

		// Create a mock executor that throws an exception from the task
		def executor = Mock(java.util.concurrent.ExecutorService) {
			submit(_) >> { Runnable task ->
				// Execute the task, but in a way that exceptions escape to the outer try-catch
				throw new RuntimeException("Test monitoring error")
			}
			close() >> {}
		}

		when:
		// Replace executor creation with our mock
		MonitorEventService.metaClass.static.newExecutor = { -> executor }
		callMonitorEvents(100)

		then:
		// Verify no transaction processing happened
		0 * mockEventRepo.save(_)
		0 * mockBlockHeightRepo.save(_)

		cleanup:
		MonitorEventService.metaClass = null
	}

	def "monitorEvents should handle exceptions in processNewTransactions"() {
		given:
		// Set up valid block height
		def blockHeight = 100L
		mockBlockHeightRepo.get() >> blockHeight

		// Set up pending queue with test data that will succeed
		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		def pendingTx = createTransaction(blockHeight + 1) // Block 101
		pendingQueue.add(pendingTx)

		// Mock transactions queue to throw exception when take() is called
		def transactionsQueue = Mock(BlockingQueue)
		transactionsQueue.take() >> { throw new RuntimeException("Test exception in processNewTransactions") }

		// Mock repository behavior
		mockEventLogRepo.getFilterLogs(blockHeight + 1) >> pendingQueue
		mockEventLogRepo.subscribe() >> transactionsQueue
		mockEventRepo.save(_) >> true
		mockBlockHeightRepo.save(_) >> true

		// Create executor mock that executes submitted tasks immediately
		def executor = Mock(java.util.concurrent.ExecutorService) {
			submit(_) >> { Runnable task ->
				task.run()
				return null
			}
			close() >> {}
		}

		when:
		// Replace executor creation with our mock
		MonitorEventService.metaClass.static.newExecutor = { -> executor }
		callMonitorEvents(100)

		then:
		// Verify pending transaction was processed successfully
		1 * mockEventRepo.save({ it.transactionHash == pendingTx.events[0].transactionHash }) >> true
		1 * mockBlockHeightRepo.save({ it.blockNumber == 101L }) >> true
		1 * mockLogger.info("Success to register block number: {}", 101L)

		// Verify the exception from processNewTransactions was caught properly
		// and no further processing was done after that
		noExceptionThrown()

		cleanup:
		MonitorEventService.metaClass = null
	}

	private void callMonitorEvents(int checkInterval) {
		def method = MonitorEventService.class.getDeclaredMethod(
				"monitorEvents",
				int.class
				)
		method.setAccessible(true)
		method.invoke(interactor, checkInterval)
	}

	def "monitorEvents should log error when exception occurs while getting filter logs"() {
		given:
		// Set up valid block height
		def blockHeight = 100L
		mockBlockHeightRepo.get() >> blockHeight

		// Set up event log repo to succeed on subscribe but throw on getFilterLogs
		def transactionsQueue = new LinkedBlockingQueue<Transaction>()
		mockEventLogRepo.subscribe() >> transactionsQueue
		mockEventLogRepo.getFilterLogs(blockHeight + 1) >> { throw new RuntimeException("Test filter logs error") }

		when:
		callMonitorEvents(100)

		then:
		// Verify the error is logged with the correct message
		1 * mockLogger.error("Error in monitoring: {}", "Test filter logs error")

		// Ensure other methods aren't called after the exception
		0 * mockEventRepo.save(_)
		0 * mockBlockHeightRepo.save(_)

		// Sleep should still be called in the finally block
		noExceptionThrown()
	}

	def "monitorEvents should not call processNewTransactions when processPendingTransactions returns null"() {
		given:
		// Set up valid block height
		def blockHeight = 100L
		mockBlockHeightRepo.get() >> blockHeight

		// Set up a pending queue with a transaction that has block height 0
		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		def txWithZeroHeight = createTransaction(0L) // Block 0
		pendingQueue.add(txWithZeroHeight)

		// Mock transactions queue that should never be accessed
		def transactionsQueue = Mock(BlockingQueue)

		// Mock repository behavior
		mockEventLogRepo.getFilterLogs(blockHeight + 1) >> pendingQueue
		mockEventLogRepo.subscribe() >> transactionsQueue

		// Create executor mock that executes submitted tasks immediately
		def executor = Mock(java.util.concurrent.ExecutorService) {
			submit(_) >> { Runnable task ->
				task.run()
				return null
			}
			close() >> {}
		}

		when:
		// Replace executor creation with our mock
		MonitorEventService.metaClass.static.newExecutor = { -> executor }
		callMonitorEvents(100)

		then:
		// Verify the error for block height 0 is logged
		1 * mockLogger.info("Pending block height Number is zero")

		// The key verification: processNewTransactions should not be called
		// We check this by ensuring take() is never called on the transactionsQueue
		0 * transactionsQueue.take()

		cleanup:
		MonitorEventService.metaClass = null
	}

	def "monitorEvents should run without errors when all operations succeed"() {
		given:
		// Set up valid block height
		def blockHeight = 100L
		mockBlockHeightRepo.get() >> blockHeight

		// Set up pending queue with test data
		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		def pendingTx = createTransaction(blockHeight + 1) // Block 101
		pendingQueue.add(pendingTx)

		// Create a transactions queue that completes normally
		def transactionsQueue = Mock(BlockingQueue)
		// Configure it to throw InterruptedException after one take() to exit the loop cleanly
		transactionsQueue.take() >> { throw new InterruptedException("Normal exit") }

		// Configure repository success paths
		mockEventLogRepo.getFilterLogs(blockHeight + 1) >> pendingQueue
		mockEventLogRepo.subscribe() >> transactionsQueue
		mockEventRepo.save(_) >> true
		mockBlockHeightRepo.save(_) >> true

		// Create executor mock that executes tasks immediately
		def executor = Mock(ExecutorService) {
			submit(_) >> { Runnable task ->
				task.run()
				return null
			}
			close() >> {}
		}

		when:
		// Replace executor creation with our mock
		MonitorEventService.metaClass.static.newExecutor = { -> executor }
		callMonitorEvents(100)

		then:
		// Verify pending transaction was processed successfully
		1 * mockEventRepo.save({ it.transactionHash == pendingTx.events[0].transactionHash }) >> true
		1 * mockBlockHeightRepo.save({ it.blockNumber == 101L }) >> true

		// The key verification: the error log should NOT be called
		0 * mockLogger.error("Error in monitoring: {}", _)

		// Normal flow logs should be present
		1 * mockLogger.info("Get blockheight: {}", blockHeight)
		1 * mockLogger.info("Success to register block number: {}", 101L)

		cleanup:
		MonitorEventService.metaClass = null
	}

	def "savePendingTransaction should return false when eventRepository.save fails"() {
		given:
		// Set up valid block height
		def blockHeight = 100L
		mockBlockHeightRepo.get() >> blockHeight

		// Create a valid transaction with event
		def event = Event.builder().transactionHash("0x123").name("TestEvent").logIndex(1).blockTimestamp(123456789L).nonIndexedValues("{\"traceId\":[]}").build()

		def blockHeightObj = BlockHeight.builder().blockNumber(blockHeight).build()
		def transaction = Transaction.builder().events(List.of(event)).blockHeight(blockHeightObj).build()

		def pendingQueue = new LinkedBlockingQueue<Transaction>()
		pendingQueue.add(transaction)
		def transactionsQueue = new LinkedBlockingQueue<Transaction>()

		// Mock repository responses
		mockEventLogRepo.getFilterLogs(blockHeight + 1) >> pendingQueue
		mockEventLogRepo.subscribe() >> transactionsQueue

		// Make eventRepository.save return false to trigger the failure
		mockEventRepo.save(event) >> true

		when:
		callMonitorEvents(100)

		then:
		noExceptionThrown()
	}

	// Helper method to call savePendingTransactionBlockNumber via reflection
	private boolean callSavePendingTransactionBlockNumber(BlockHeight blockHeight) {
		def method = MonitorEventService.class.getDeclaredMethod(
				"savePendingTransactionBlockNumber",
				BlockHeight.class
				)
		method.setAccessible(true)
		return method.invoke(interactor, blockHeight)
	}

	// Helper method to call savePendingTransaction via reflection
	private boolean callSavePendingTransaction(Transaction tx) {
		def method = MonitorEventService.class.getDeclaredMethod(
				"savePendingTransaction",
				Transaction.class
				)
		method.setAccessible(true)
		return method.invoke(interactor, tx)
	}

	// Helper methods for accessing private methods via reflection
	private BlockHeight callProcessPendingTransactions(BlockingQueue<Transaction> queue, int interval) {
		def method = MonitorEventService.class.getDeclaredMethod(
				"processPendingTransactions",
				BlockingQueue.class,
				int.class
				)
		method.setAccessible(true)
		return method.invoke(interactor, queue, interval)
	}

	private boolean callSaveTransaction(Transaction tx) {
		def method = MonitorEventService.class.getDeclaredMethod("saveTransaction", Transaction.class)
		method.setAccessible(true)
		return method.invoke(interactor, tx)
	}

	private String callFetchTraceId(String nonIndexedValues) {
		def method = MonitorEventService.class.getDeclaredMethod("fetchTraceId", String.class)
		method.setAccessible(true)
		return method.invoke(interactor, nonIndexedValues)
	}

	private void setRunning(boolean value) {
		def field = MonitorEventService.class.getDeclaredField("running")
		field.setAccessible(true)
		AtomicBoolean running = field.get(interactor)
		running.set(value)
	}

	// Helper method to create test data
	private Transaction createTransaction(Long blockNumber) {
		def event = Event.builder()
				.name("TestEvent")
				.transactionHash("0xabc123")
				.blockTimestamp(1626912345L)
				.logIndex(1)
				.nonIndexedValues('{"traceId":[84,101,115,116]}')
				.build()

		def blockHeight = BlockHeight.builder()
				.blockNumber(blockNumber)
				.build()

		return Transaction.builder()
				.events([event])
				.blockHeight(blockHeight)
				.build()
	}


	private static Transaction createTransactionWithEmptyHash(Long blockNumber) {
		def event = Event.builder()
				.name("TestEvent")
				.transactionHash("") // Empty transaction hash
				.blockTimestamp(1626912345L)
				.logIndex(1)
				.nonIndexedValues('{"traceId":[84,101,115,116]}')
				.build()

		def blockHeight = BlockHeight.builder()
				.blockNumber(blockNumber)
				.build()

		return Transaction.builder()
				.events([event])
				.blockHeight(blockHeight)
				.build()
	}

	// Helper method to call processNewTransactions via reflection
	private void callProcessNewTransactions(BlockingQueue<Transaction> queue, int interval) {
		def method = MonitorEventService.class.getDeclaredMethod(
				"processNewTransactions",
				BlockingQueue.class,
				int.class
				)
		method.setAccessible(true)
		method.invoke(interactor, queue, interval)
	}
}
