package com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain.abi

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3AbiRepository
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptor
import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.exception.ConfigurationException
import com.decurret_dcp.dcjpy.bcmonitoring.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import spock.lang.Specification

@Testcontainers
class DownloadAbiServiceIntegrationSpec extends Specification {

	static S3Client s3Client
	static BcmonitoringConfigurationProperties properties
	static BcmonitoringConfigurationProperties.Aws aws
	static BcmonitoringConfigurationProperties.Aws.S3 s3Config
	static LoggingService loggingService
	static S3AbiRepository s3AbiRepository
	static AbiParser abiParser
	static DownloadAbiService downloadAbiService
	static final String BUCKET_NAME = "abijson-local-bucket"

	@DynamicPropertySource
	static void applicationProperties(DynamicPropertyRegistry registry) {
	}

	def setupSpec() {
		String localStackPort = AdhocHelper.getLocalStackPort()
		s3Client = S3Client.builder()
				.region(Region.AP_NORTHEAST_1)
				.endpointOverride(URI.create("http://localhost:${localStackPort}"))
				.forcePathStyle(true)
				.build()

		loggingService = new LoggingService(new BcmonitoringConfigurationProperties())
		s3AbiRepository = new S3ClientAdaptor(s3Client, loggingService)
		properties = Spy(BcmonitoringConfigurationProperties)
		abiParser = new AbiParser(properties)

		aws = Spy(BcmonitoringConfigurationProperties.Aws)
		s3Config = Spy(BcmonitoringConfigurationProperties.Aws.S3)

		properties.getAws() >> aws
		aws.getS3() >> s3Config
		s3Config.getBucketName() >> BUCKET_NAME

		downloadAbiService = new DownloadAbiService(loggingService, s3AbiRepository, abiParser, properties)
	}

	def cleanupSpec() {
		s3Client.close()
		AdhocHelper.cleanupSpec()
	}

	def "should successfully download and process ABI files from S3"() {
		when: "Executing the download service"
		downloadAbiService.execute()

		then: "No exceptions are thrown and ABI files are processed"
		noExceptionThrown()
		// Optionally, add assertions or log checks if your service provides hooks
	}

	def "should throw ConfigurationException when bucket name is not configured"() {
		given:
		BcmonitoringConfigurationProperties propertiesNull = Spy(BcmonitoringConfigurationProperties)
		BcmonitoringConfigurationProperties.Aws awsNull = Spy(BcmonitoringConfigurationProperties.Aws)
		BcmonitoringConfigurationProperties.Aws.S3 s3ConfigNull = Spy(BcmonitoringConfigurationProperties.Aws.S3)

		propertiesNull.getAws() >> awsNull
		awsNull.getS3() >> s3ConfigNull
		s3ConfigNull.getBucketName() >> null

		DownloadAbiService downloadAbiServiceThrow = new DownloadAbiService(loggingService, s3AbiRepository, abiParser, propertiesNull)

		when:
		downloadAbiServiceThrow.execute()

		then:
		thrown(ConfigurationException)
	}

	def "should skip files with non-JSON extensions"() {
		given: "A non-JSON file exists in the bucket"
		s3Client.putObject({ it.bucket(BUCKET_NAME).key("3000/README.txt").build() },
		RequestBody.fromString("not json"))

		when:
		downloadAbiService.execute()

		then:
		noExceptionThrown()
		// Optionally, check logs or side effects if needed
	}

	def "should throw IOException for invalid JSON ABI file"() {
		given: "An invalid JSON file in the bucket"
		s3Client.putObject({ it.bucket(BUCKET_NAME).key("3000/Invalid.json").build() },
		RequestBody.fromString("{invalid json"))

		when:
		downloadAbiService.execute()

		then:
		thrown(IOException)
	}
}
