package com.decurret_dcp.dcjpy.bcmonitoring.infrastructure.dynamodb

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDao
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight
import com.decurret_dcp.dcjpy.bcmonitoring.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*
import spock.lang.Specification

@Testcontainers
class BlockHeightDaoIntegrationSpec extends Specification {

	static DynamoDbClient dynamoDbClient
	static BcmonitoringConfigurationProperties mockProperties
	static BcmonitoringConfigurationProperties.Aws mockAws
	static BcmonitoringConfigurationProperties.Aws.Dynamodb mockDynamodb
	static LoggingService mockLogger
	static BlockHeightDao blockHeightDao
	static final String TABLE_NAME = "test-block-heights"
	static final String TABLE_NAME_WITH_PREFIX = "prefix-test-block-heights"

	@DynamicPropertySource
	static void applicationProperties(DynamicPropertyRegistry registry) {
		// Add any dynamic properties if needed
	}

	def setupSpec() {
		String localStackEndpoint = "http://localhost:4566"
		println("Connecting to LocalStack at: " + localStackEndpoint)

		dynamoDbClient = DynamoDbClient.builder()
				.region(Region.AP_NORTHEAST_1)
				.endpointOverride(URI.create(localStackEndpoint))
				.credentialsProvider(StaticCredentialsProvider.create(
				AwsBasicCredentials.create("dummy", "dummy")))
				.build()

		try {
			def tables = dynamoDbClient.listTables()
			println("Successfully connected to DynamoDB. Existing tables: " + tables.tableNames())
		} catch (Exception e) {
			println("Error connecting to DynamoDB: " + e.getMessage())
			e.printStackTrace()
		}

		mockProperties = Mock(BcmonitoringConfigurationProperties)
		mockAws = Mock(BcmonitoringConfigurationProperties.Aws)
		mockDynamodb = Mock(BcmonitoringConfigurationProperties.Aws.Dynamodb)
		mockLogger = Mock(LoggingService)

		mockProperties.getAws() >> mockAws
		mockAws.getDynamodb() >> mockDynamodb
		mockDynamodb.getBlockHeightTableName() >> TABLE_NAME
		mockDynamodb.getTableNameWithPrefix(TABLE_NAME) >> TABLE_NAME_WITH_PREFIX

		blockHeightDao = new BlockHeightDao(dynamoDbClient, mockProperties, mockLogger)

		AdhocHelper.createBlockHeightTable(dynamoDbClient, TABLE_NAME_WITH_PREFIX)
	}

	def cleanupSpec() {
		try {
			// Delete the table
			dynamoDbClient.deleteTable(DeleteTableRequest.builder()
					.tableName(TABLE_NAME_WITH_PREFIX)
					.build())
		} catch (Exception e) {
			// Ignore errors during cleanup
		}

		dynamoDbClient.close()
	}

	def setup() {
		// Clear the table before each test
		try {
			def scanResult = dynamoDbClient.scan(ScanRequest.builder()
					.tableName(TABLE_NAME_WITH_PREFIX)
					.build())

			for (def item : scanResult.items()) {
				dynamoDbClient.deleteItem(DeleteItemRequest.builder()
						.tableName(TABLE_NAME_WITH_PREFIX)
						.key(Map.of("id", item.get("id")))
						.build())
			}
		} catch (Exception e) {
			// Ignore errors during setup
		}
	}

	def "should successfully save and retrieve a block height"() {
		given: "A block height to save"
		def id = 1L
		def blockNumber = 12345L
		def blockHeight = BlockHeight.builder()
				.id(id)
				.blockNumber(blockNumber)
				.build()

		when: "Saving the block height"
		def saveResult = blockHeightDao.save(blockHeight)

		then: "The save operation should succeed"
		saveResult == true

		when: "Getting the block height"
		def retrievedBlockNumber = blockHeightDao.get()

		then: "The retrieved block number should match what was saved"
		retrievedBlockNumber == blockNumber
	}

	def "should update existing block height"() {
		given: "An existing block height"
		def id = 1L
		def initialBlockNumber = 12345L
		def blockHeight = BlockHeight.builder()
				.id(id)
				.blockNumber(initialBlockNumber)
				.build()
		blockHeightDao.save(blockHeight)

		and: "An updated block height"
		def updatedBlockNumber = 67890L
		def updatedBlockHeight = BlockHeight.builder()
				.id(id)
				.blockNumber(updatedBlockNumber)
				.build()

		when: "Updating the block height"
		def updateResult = blockHeightDao.save(updatedBlockHeight)

		then: "The update operation should succeed"
		updateResult == true

		when: "Getting the block height"
		def retrievedBlockNumber = blockHeightDao.get()

		then: "The retrieved block number should match the updated value"
		retrievedBlockNumber == updatedBlockNumber
	}

	def "should return 0 when no block heights found"() {
		when: "Getting block height from empty table"
		def result = blockHeightDao.get()

		then: "Should return 0"
		result == 0L
	}
}
