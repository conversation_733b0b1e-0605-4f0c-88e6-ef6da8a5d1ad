package com.decurret_dcp.dcjpy.bcmonitoring.application.blockchain

import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import com.fasterxml.jackson.databind.ObjectMapper
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll

class TraceIdExtractorServiceSpec extends Specification {

	ObjectMapper objectMapper
	LoggingService loggingService

	@Subject
	TraceIdExtractorService service

	def setup() {
		objectMapper = new ObjectMapper()
		loggingService = Mock(LoggingService)
		service = new TraceIdExtractorService(loggingService, objectMapper)
	}

	def "should extract trace ID from valid JSON string"() {
		given: "a JSON string containing traceId"
		def jsonString = '{"traceId":[51,53,51,100,97,99,102,54,55,50,54,57,51,54,51,102,99,55,53,98,99,100,102,57,99,56,102,57,48,53,50,99]}'

		when: "fetchTraceId is called"
		def result = service.fetchTraceId(jsonString)

		then: "it should return the correct trace ID"
		result == "353dacf67269363fc75bcdf9c8f9052c"
	}

	def "should convert UTF-8 characters in range 0-16"() {
		given: "a JSON string with bytes representing hex characters"
		def jsonString = '{"traceId":[48,49,50,51,52,53,54,55,56,57,97,98,99,100,101,102]}'

		when: "fetchTraceId is called"
		def result = service.fetchTraceId(jsonString)

		then: "it should correctly convert to UTF-8 string"
		result == "0123456789abcdef"
	}

	def "should filter out zero bytes from trace ID"() {
		given: "a JSON string with zero bytes"
		def jsonString = '{"traceId":[84,0,101,0,115,0,116,0]}'

		when: "fetchTraceId is called"
		def result = service.fetchTraceId(jsonString)

		then: "it should filter out the zeros"
		result == "Test"
	}

	def "should return empty string when traceId is empty"() {
		given: "a JSON string with empty traceId array"
		def jsonString = '{"traceId":[]}'

		when: "fetchTraceId is called"
		def result = service.fetchTraceId(jsonString)

		then: "it should return an empty string"
		result == ""
	}

	def "should return empty string when traceId is missing"() {
		given: "a JSON string without traceId"
		def jsonString = '{}'

		when: "fetchTraceId is called"
		def result = service.fetchTraceId(jsonString)

		then: "it should return an empty string"
		result == ""
	}

	def "should return empty string when JSON is invalid"() {
		given: "an invalid JSON string"
		def jsonString = "invalid json"

		when: "fetchTraceId is called"
		def result = service.fetchTraceId(jsonString)

		then: "it should return an empty string and log error"
		result == ""
	}

	@Unroll
	def "should handle trace ID #scenario"() {
		expect: "correct parsing for different inputs"
		service.fetchTraceId(input) == expected

		where:
		scenario          | input                                      | expected
		"normal text"     | '{"traceId":[116,101,115,116]}'            | "test"
		"uppercase"       | '{"traceId":[84,69,83,84]}'                | "TEST"
		"with zeros"      | '{"traceId":[0,0,116,101,115,116,0,0]}'    | "test"
		"empty object"    | '{}'                                       | ""
		"empty string"    | ""                                         | ""
	}
}
