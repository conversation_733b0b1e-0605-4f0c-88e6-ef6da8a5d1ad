package com.decurret_dcp.dcjpy.bcmonitoring.infrastructure.dynamodb

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDao
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event
import com.decurret_dcp.dcjpy.bcmonitoring.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.AttributeValue
import spock.lang.Specification

@Testcontainers
class EventDaoIntegrationSpec extends Specification {

	static DynamoDbClient dynamoDbClient
	static BcmonitoringConfigurationProperties mockProperties
	static BcmonitoringConfigurationProperties.Aws mockAws
	static BcmonitoringConfigurationProperties.Aws.Dynamodb mockDynamodb
	static LoggingService mockLogger
	static EventDao eventDao
	static final String TABLE_NAME = "test-events"
	static final String TABLE_NAME_WITH_PREFIX = "prefix-test-events"

	@DynamicPropertySource
	static void applicationProperties(DynamicPropertyRegistry registry) {
		// Add any dynamic properties if needed
	}

	def setupSpec() {
		String localStackPort = AdhocHelper.getLocalStackPort()
		dynamoDbClient = DynamoDbClient.builder()
				.region(Region.AP_NORTHEAST_1)
				.endpointOverride(URI.create("http://localhost:${localStackPort}"))
				.build()

		// Set up mocks
		mockProperties = Mock(BcmonitoringConfigurationProperties)
		mockAws = Mock(BcmonitoringConfigurationProperties.Aws)
		mockDynamodb = Mock(BcmonitoringConfigurationProperties.Aws.Dynamodb)
		mockLogger = Mock(LoggingService)

		mockProperties.getAws() >> mockAws
		mockAws.getDynamodb() >> mockDynamodb
		mockDynamodb.getEventsTableName() >> TABLE_NAME
		mockDynamodb.getTableNameWithPrefix(TABLE_NAME) >> TABLE_NAME_WITH_PREFIX

		eventDao = new EventDao(dynamoDbClient, mockProperties, mockLogger)

		// Create the events table if it doesn't exist
		AdhocHelper.createEventsTable(dynamoDbClient, TABLE_NAME_WITH_PREFIX)
	}

	def cleanupSpec() {
		dynamoDbClient.close()
		AdhocHelper.cleanupSpec()
	}

	def setup() {
		// Setup for each test
	}

	def cleanup() {
		// Cleanup after each test
	}

	def "should successfully save and retrieve an event"() {
		given: "An event to save"
		def transactionHash = "0x123456789abcdef"
		def logIndex = 1
		def event = Event.builder()
				.transactionHash(transactionHash)
				.logIndex(logIndex)
				.name("TestEvent")
				.indexedValues("indexed values")
				.nonIndexedValues("non-indexed values")
				.blockTimestamp(1234567890L)
				.log("log content")
				.build()

		when: "Saving the event"
		def saveResult = eventDao.save(event)

		then: "The save operation should succeed"
		saveResult == true

		and: "The event should be retrievable from DynamoDB"
		Map<String, AttributeValue> record = AdhocHelper.getEventItem(dynamoDbClient, TABLE_NAME_WITH_PREFIX, transactionHash, logIndex)
		record != null
		record.get("transactionHash").s() == transactionHash
		record.get("logIndex").n() == logIndex.toString()
		record.get("name").s() == "TestEvent"
		record.get("indexedValues").s() == "indexed values"
		record.get("nonIndexedValues").s() == "non-indexed values"
		record.get("blockTimestamp").n() == "1234567890"
		record.get("log").s() == "log content"

		cleanup: "Remove the test event"
		// Add cleanup code if needed
	}

	def "should handle multiple events with same transaction hash but different log indexes"() {
		given: "Multiple events with the same transaction hash"
		def transactionHash = "0xmultiple123"
		def event1 = Event.builder()
				.transactionHash(transactionHash)
				.logIndex(1)
				.name("TestEvent1")
				.blockTimestamp(1234567890L)
				.indexedValues("indexed values 1")
				.nonIndexedValues("non-indexed values 1")
				.log("log content 1")
				.build()

		def event2 = Event.builder()
				.transactionHash(transactionHash)
				.logIndex(2)
				.name("TestEvent2")
				.blockTimestamp(1234567890L)
				.indexedValues("indexed values 2")
				.nonIndexedValues("non-indexed values 2")
				.log("log content 2")
				.build()

		when: "Saving both events"
		def saveResult1 = eventDao.save(event1)
		def saveResult2 = eventDao.save(event2)

		then: "Both save operations should succeed"
		saveResult1
		saveResult2

		and: "Both events should be retrievable from DynamoDB"
		Map<String, AttributeValue> record1 = AdhocHelper.getEventItem(dynamoDbClient, TABLE_NAME_WITH_PREFIX, transactionHash, 1)
		record1 != null
		record1.get("name").s() == "TestEvent1"

		Map<String, AttributeValue> record2 = AdhocHelper.getEventItem(dynamoDbClient, TABLE_NAME_WITH_PREFIX, transactionHash, 2)
		record2 != null
		record2.get("name").s() == "TestEvent2"

		cleanup: "Remove the test events"
		// Add cleanup code if needed
	}
}
