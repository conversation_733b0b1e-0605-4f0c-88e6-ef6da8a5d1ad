package com.decurret_dcp.dcjpy.bcmonitoring.infrastructure.ethereum

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDao
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepository
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepositoryImpl
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction
import com.decurret_dcp.dcjpy.bcmonitoring.exception.BlockchainException
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import java.util.concurrent.LinkedBlockingQueue
import spock.lang.Specification

class EventLogRepositorySpec extends Specification {

	LoggingService mockLogger
	EthEventLogDao mockEventLogDao
	EventLogRepository repository

	def setup() {
		mockLogger = Mock(LoggingService)
		mockEventLogDao = Mock(EthEventLogDao)
		repository = new EventLogRepositoryImpl(mockLogger, mockEventLogDao)
	}

	def "Subscribe should return transactions when successful"() {
		given:
		def transactions = new LinkedBlockingQueue<Transaction>()

		when:
		def result = repository.subscribe()

		then:
		1 * mockEventLogDao.subscribeAll() >> transactions
		result == transactions
	}

	def "Subscribe should throw BlockchainException when DAO error occurs"() {
		given:
		def mockException = new RuntimeException("mock error")

		when:
		repository.subscribe()

		then:
		1 * mockEventLogDao.subscribeAll() >> { throw mockException }
		1 * mockLogger.error(_ as String, mockException)
		def exception = thrown(BlockchainException)
		exception.cause == mockException
	}

	def "GetFilterLogs should return transactions when successful"() {
		given:
		def blockHeight = 1000L
		def transactions = new LinkedBlockingQueue<Transaction>()

		when:
		def result = repository.getFilterLogs(blockHeight)

		then:
		1 * mockEventLogDao.getPendingTransactions(blockHeight) >> transactions
		result == transactions
	}

	def "GetFilterLogs should throw BlockchainException when DAO error occurs"() {
		given:
		def blockHeight = 1000L
		def mockException = new IOException("mock error")

		when:
		repository.getFilterLogs(blockHeight)

		then:
		1 * mockEventLogDao.getPendingTransactions(blockHeight) >> { throw mockException }
		1 * mockLogger.error(_ as String, mockException)
		def exception = thrown(BlockchainException)
		exception.cause == mockException
	}
}
