package com.decurret_dcp.dcjpy.bcmonitoring.infrastructure.ethereum

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDao
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventSignatureRegistry
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import com.fasterxml.jackson.databind.ObjectMapper
import io.reactivex.Flowable
import java.time.Instant
import java.util.concurrent.BlockingQueue
import java.util.concurrent.CompletableFuture
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Type
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.protocol.Web3j
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.request.EthFilter
import org.web3j.protocol.core.methods.response.EthBlock
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt
import org.web3j.protocol.core.methods.response.EthLog
import org.web3j.protocol.core.methods.response.Log
import org.web3j.protocol.core.methods.response.TransactionReceipt
import spock.lang.Specification

class EthEventLogDaoSpec extends Specification {

	LoggingService mockLogger
	Web3j mockWeb3j
	EventSignatureRegistry mockEventRegistry
	BcmonitoringConfigurationProperties mockProperties
	BcmonitoringConfigurationProperties.Subscription mockSubscription
	Web3jConfig mockWeb3jConfig
	EthEventLogDao ethEventLogDao
	AbiParser mockAbiParser
	ObjectMapper mockObjectMapper

	def setup() {
		mockLogger = Mock(LoggingService)
		mockWeb3j = Mock(Web3j)
		mockEventRegistry = Mock(EventSignatureRegistry)
		mockProperties = Mock(BcmonitoringConfigurationProperties)
		mockSubscription = Mock(BcmonitoringConfigurationProperties.Subscription)
		mockWeb3jConfig = Mock(Web3jConfig)
		mockAbiParser = Mock(AbiParser)
		mockObjectMapper = Mock(ObjectMapper)

		mockProperties.getSubscription() >> mockSubscription
		mockSubscription.getAllowableBlockTimestampDiffSec() >> "60"
		mockWeb3jConfig.createWebSocketWeb3j() >> mockWeb3j

		ethEventLogDao = new EthEventLogDao(mockLogger, mockProperties, mockWeb3jConfig, mockEventRegistry, mockAbiParser, mockObjectMapper)
	}

	def "convBlock2EventEntities should handle empty transaction lists"() {
		given:
		def mockBlock = Mock(EthBlock.Block)

		when:
		def result = ethEventLogDao.convBlock2EventEntities(mockBlock)

		then:
		1 * mockBlock.getTransactions() >> []
		result != null
		result.isEmpty()
	}


	def "convBlock2EventEntities should handle missing transaction receipts"() {
		given:
		def mockBlock = Mock(EthBlock.Block)
		def txHash = "0xabc123"

		// Create real transaction object
		def txObject = new EthBlock.TransactionObject()
		txObject.hash = txHash

		def mockRequest = Mock(Request)
		def mockFuture = Mock(CompletableFuture)
		def mockReceipt = Mock(EthGetTransactionReceipt)

		when:
		def result = ethEventLogDao.convBlock2EventEntities(mockBlock)

		then:
		1 * mockBlock.getTransactions() >> [txObject]
		1 * mockWeb3j.ethGetTransactionReceipt(txHash) >> mockRequest
		1 * mockRequest.sendAsync() >> mockFuture
		1 * mockFuture.get() >> mockReceipt
		1 * mockReceipt.getTransactionReceipt() >> Optional.empty()

		result != null
		result.isEmpty()
	}

	def "convBlock2EventEntities should process transactions with valid logs"() {
		given: "A block containing a transaction"
		def txHash = "0xabc123"
		def blockTimestamp = 1626912345L

		def txObject = new EthBlock.TransactionObject()
		txObject.hash = txHash

		def blockObj = new EthBlock.Block()
		blockObj.timestamp = BigInteger.valueOf(blockTimestamp)
		blockObj.number = BigInteger.valueOf(1000)
		blockObj.transactions = [txObject]

		and: "A log for this transaction"
		def log = new Log()
		log.transactionHash = txHash
		log.topics = ["0xevent123"]
		log.address = "0x1234567890abcdef"
		log.blockNumber = "0x3e8"
		log.logIndex = "0x1"

		and: "Mocked API responses"
		def mockRequest = Mock(Request)
		def mockFuture = Mock(CompletableFuture)
		def mockReceipt = Mock(EthGetTransactionReceipt)
		def mockTxReceipt = Mock(TransactionReceipt)

		and: "Expected event to be returned"
		def expectedEvent = Event.builder()
				.name("TestEvent")
				.transactionHash(txHash)
				.blockTimestamp(blockTimestamp)
				.build()

		and: "A spy that returns the expected event"
		def ethEventLogDaoSpy = Spy(ethEventLogDao) {
			convertEthLogToEventEntity(_) >> expectedEvent
		}

		when: "Converting the block to events"
		def result = ethEventLogDaoSpy.convBlock2EventEntities(blockObj)

		then: "The Web3j API is called correctly"
		1 * mockWeb3j.ethGetTransactionReceipt(txHash) >> mockRequest
		1 * mockRequest.sendAsync() >> mockFuture
		1 * mockFuture.get() >> mockReceipt
		1 * mockReceipt.getTransactionReceipt() >> Optional.of(mockTxReceipt)
		1 * mockTxReceipt.getLogs() >> [log]

		and: "The result contains the expected event"
		result.size() == 1
		result[0].name == "TestEvent"
		result[0].transactionHash == txHash
	}

	def "convBlock2EventEntities should process transactions with logs event transactionHash null"() {
		given: "A block containing a transaction"
		def txHash = "0xabc123"
		def blockTimestamp = 1626912345L

		def txObject = new EthBlock.TransactionObject()
		txObject.hash = txHash

		def blockObj = new EthBlock.Block()
		blockObj.timestamp = BigInteger.valueOf(blockTimestamp)
		blockObj.number = BigInteger.valueOf(1000)
		blockObj.transactions = [txObject]

		and: "A log for this transaction"
		def log = new Log()
		log.transactionHash = txHash
		log.topics = ["0xevent123"]
		log.address = "0x1234567890abcdef"
		log.blockNumber = "0x3e8"
		log.logIndex = "0x1"

		and: "Mocked API responses"
		def mockRequest = Mock(Request)
		def mockFuture = Mock(CompletableFuture)
		def mockReceipt = Mock(EthGetTransactionReceipt)
		def mockTxReceipt = Mock(TransactionReceipt)

		and: "Expected event to be returned"
		def expectedEvent = Event.builder()
				.name("TestEvent")
				.transactionHash(null)
				.blockTimestamp(blockTimestamp)
				.build()

		and: "A spy that returns the expected event"
		def ethEventLogDaoSpy = Spy(ethEventLogDao) {
			convertEthLogToEventEntity(_) >> expectedEvent
		}

		when: "Converting the block to events"
		def result = ethEventLogDaoSpy.convBlock2EventEntities(blockObj)

		then: "The Web3j API is called correctly"
		1 * mockWeb3j.ethGetTransactionReceipt(txHash) >> mockRequest
		1 * mockRequest.sendAsync() >> mockFuture
		1 * mockFuture.get() >> mockReceipt
		1 * mockReceipt.getTransactionReceipt() >> Optional.of(mockTxReceipt)
		1 * mockTxReceipt.getLogs() >> [log]

		and: "The result is empty"
		result.isEmpty()
	}

	def "convBlock2EventEntities should handle exceptions during log processing"() {
		given: "A block containing a transaction"
		def txHash = "0xabc123"
		def blockTimestamp = 1626912345L

		def txObject = new EthBlock.TransactionObject()
		txObject.hash = txHash

		def blockObj = new EthBlock.Block()
		blockObj.timestamp = BigInteger.valueOf(blockTimestamp)
		blockObj.number = BigInteger.valueOf(1000)
		blockObj.transactions = [txObject]

		and: "A log for this transaction"
		def log = new Log()
		log.transactionHash = txHash
		log.topics = ["0xevent123"]
		log.address = "0x1234567890abcdef"
		log.blockNumber = "0x3e8"
		log.logIndex = "0x1"

		and: "Mocked API responses"
		def mockRequest = Mock(Request)
		def mockFuture = Mock(CompletableFuture)
		def mockReceipt = Mock(EthGetTransactionReceipt)
		def mockTxReceipt = Mock(TransactionReceipt)

		and: "A spy that throws an exception"
		def ethEventLogDaoSpy = Spy(ethEventLogDao) {
			convertEthLogToEventEntity(_) >> { throw new Exception("Test exception") }
		}

		when: "Converting the block to events"
		def result = ethEventLogDaoSpy.convBlock2EventEntities(blockObj)

		then: "The Web3j API is called correctly"
		1 * mockWeb3j.ethGetTransactionReceipt(txHash) >> mockRequest
		1 * mockRequest.sendAsync() >> mockFuture
		1 * mockFuture.get() >> mockReceipt
		1 * mockReceipt.getTransactionReceipt() >> Optional.of(mockTxReceipt)
		1 * mockTxReceipt.getLogs() >> [log]

		and: "The error is logged"
		1 * mockLogger.error("Error processing log for transaction {}", txHash)

		and: "The result is empty because of the exception"
		result.isEmpty()
	}

	def "convBlock2EventEntities should handle exceptions during transaction processing"() {
		given: "A block containing a transaction"
		def txHash = "0xabc123"
		def blockTimestamp = 1626912345L

		def txObject = new EthBlock.TransactionObject()
		txObject.hash = txHash

		def blockObj = new EthBlock.Block()
		blockObj.timestamp = BigInteger.valueOf(blockTimestamp)
		blockObj.number = BigInteger.valueOf(1000)
		blockObj.transactions = [txObject]

		and: "Mocked API responses that throw exception"
		def mockRequest = Mock(Request)
		def exception = new RuntimeException("Test transaction exception")

		when: "Converting the block to events"
		def result = ethEventLogDao.convBlock2EventEntities(blockObj)

		then: "The Web3j API is called and throws exception"
		1 * mockWeb3j.ethGetTransactionReceipt(txHash) >> mockRequest
		1 * mockRequest.sendAsync() >> { throw exception }

		and: "The error is logged"
		1 * mockLogger.error("Error processing transaction", exception)

		and: "The result is empty because of the exception"
		result.isEmpty()
	}

	def "convBlock2EventEntities should skip events with empty transaction hash"() {
		given: "A block containing a transaction"
		def txHash = "0xabc123"
		def blockTimestamp = 1626912345L

		def txObject = new EthBlock.TransactionObject()
		txObject.hash = txHash

		def blockObj = new EthBlock.Block()
		blockObj.timestamp = BigInteger.valueOf(blockTimestamp)
		blockObj.number = BigInteger.valueOf(1000)
		blockObj.transactions = [txObject]

		and: "A log for this transaction"
		def log = new Log()
		log.transactionHash = txHash
		log.topics = ["0xevent123"]
		log.address = "0x1234567890abcdef"
		log.blockNumber = "0x3e8"
		log.logIndex = "0x1"

		and: "Mocked API responses"
		def mockRequest = Mock(Request)
		def mockFuture = Mock(CompletableFuture)
		def mockReceipt = Mock(EthGetTransactionReceipt)
		def mockTxReceipt = Mock(TransactionReceipt)

		and: "Expected event with empty transaction hash"
		def expectedEvent = Event.builder()
				.name("TestEvent")
				.transactionHash("") // Empty transaction hash
				.blockTimestamp(blockTimestamp)
				.build()

		and: "A spy that returns the expected event"
		def ethEventLogDaoSpy = Spy(ethEventLogDao) {
			convertEthLogToEventEntity(_) >> expectedEvent
		}

		when: "Converting the block to events"
		def result = ethEventLogDaoSpy.convBlock2EventEntities(blockObj)

		then: "The Web3j API is called correctly"
		1 * mockWeb3j.ethGetTransactionReceipt(txHash) >> mockRequest
		1 * mockRequest.sendAsync() >> mockFuture
		1 * mockFuture.get() >> mockReceipt
		1 * mockReceipt.getTransactionReceipt() >> Optional.of(mockTxReceipt)
		1 * mockTxReceipt.getLogs() >> [log]

		and: "Log processing information is logged"
		1 * mockLogger.info("event found tx_hash={}", txHash)
		1 * mockLogger.info("event parsed tx_hash={}, name={}", "", "TestEvent")

		and: "The result is empty because the event has an empty transaction hash"
		result.isEmpty()
	}

	def "getPendingTransactions should handle exceptions during event processing"() {
		given:
		def blockHeight = 1000L
		def txHash = "0xabc123"

		// Set up a log that will be processed
		def log = new Log()
		log.setTransactionHash(txHash)
		log.setBlockNumber("0x3e8")
		log.setLogIndex("0x1")
		log.setTopics(["0xeventSignature"])
		log.setData("0xdata")

		def logResult = Mock(EthLog.LogObject)
		logResult.get() >> log

		// Mock API responses
		def mockRequest = Mock(Request)
		def mockEthLog = Mock(EthLog)
		def mockBlockRequest = Mock(Request)
		def mockBlockResponse = Mock(EthBlock)
		def mockBlockObj = Mock(EthBlock.Block)

		// Set up all required mocks before the when block
		mockWeb3j.ethGetLogs(_) >> mockRequest
		mockRequest.send() >> mockEthLog
		mockEthLog.getLogs() >> [logResult]
		mockWeb3j.ethGetBlockByNumber(_, _) >> mockBlockRequest
		mockBlockRequest.send() >> mockBlockResponse
		mockBlockResponse.getBlock() >> mockBlockObj
		mockBlockObj.getTimestamp() >> BigInteger.valueOf(Instant.now().epochSecond)

		// Exception to be thrown during processing
		def exception = new RuntimeException("Test exception NOT containing 'No ABI event found'")
		mockEventRegistry.resolveEventName("0xeventSignature") >> { throw exception }

		when:
		def result = ethEventLogDao.getPendingTransactions(blockHeight)
		// Allow time for async processing
		sleep(1500)

		then:
		// Verify the queue is returned
		result instanceof BlockingQueue

		// Verify expected interactions
		1 * mockLogger.info("Retrieved {} logs from block height {}", _, _)

		// No exceptions should escape the async handler
		noExceptionThrown()
	}

	def "isDelayed should detect delayed blocks"() {
		given:
		def mockBlock = Mock(EthBlock.Block)
		def currentTime = Instant.now().getEpochSecond()
		def method = EthEventLogDao.class.getDeclaredMethod("isDelayed", EthBlock.Block.class, int.class)
		method.setAccessible(true)

		when: "Block is not delayed"
		1 * mockBlock.getTimestamp() >> BigInteger.valueOf(currentTime - 30)
		def result1 = method.invoke(ethEventLogDao, mockBlock, 60)

		then:
		!result1

		when: "Block is delayed"
		1 * mockBlock.getTimestamp() >> BigInteger.valueOf(currentTime - 120)
		def result2 = method.invoke(ethEventLogDao, mockBlock, 60)

		then:
		result2
	}

	def "getPendingTransactions should process logs and return transactions"() {
		given:
		def blockHeight = 1000L

		// Create a log with proper data
		def log = new Log()
		log.setTransactionHash("0xabc123")
		log.setBlockNumber("0x3e8") // Hex for 1000
		log.setLogIndex("0x1")
		log.setTopics([
			"0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"
		])
		log.setAddress("0x1234567890abcdef")

		// Create log result wrapper
		def logResult = Mock(EthLog.LogResult)
		logResult.get() >> log

		// Non-empty list of logs
		def logResults = [logResult]

		// Expected event to be returned
		def expectedEvent = Event.builder()
				.name("TestEvent")
				.transactionHash("0xabc123")
				.blockTimestamp(1626912345L)
				.build()

		// Create the spy BEFORE setting up mocks
		def ethEventLogDaoSpy = Spy(ethEventLogDao) {
			convertEthLogToEventEntity(_) >> expectedEvent
		}

		// Request chain mocks
		def mockLogRequest = Mock(Request)
		def mockEthLog = Mock(EthLog)

		when:
		def result = ethEventLogDaoSpy.getPendingTransactions(blockHeight)

		then:
		// Set up proper mock chain - order is important here
		1 * mockWeb3j.ethGetLogs(_) >> mockLogRequest
		1 * mockLogRequest.send() >> mockEthLog
		1 * mockEthLog.getLogs() >> logResults

		// Verify result
		result instanceof BlockingQueue

		// Wait for async processing
		sleep(500)
	}

	def "getPendingTransactions should process logs with valid data"() {
		given:
		def blockHeight = 1000L
		def mockRequest = Mock(Request)
		def mockEthLog = Mock(EthLog)

		// Create a Log object
		def log = new Log()
		log.setTransactionHash("0xabc123")
		log.setBlockNumber("0x3e8") // 1000 in hex
		log.setLogIndex("0x1")
		log.setTopics(["0xeventSignature"])
		log.setData("0xdata")

		// Create a log result object that returns the log
		def logResult = Mock(EthLog.LogObject)
		logResult.get() >> log

		// Block timestamp mocks
		def mockBlockRequest = Mock(Request)
		def mockBlockResponse = Mock(EthBlock)
		def mockBlockObj = Mock(EthBlock.Block)

		when:
		def result = ethEventLogDao.getPendingTransactions(blockHeight)

		then:
		1 * mockWeb3j.ethGetLogs(_) >> mockRequest
		1 * mockRequest.send() >> mockEthLog
		1 * mockEthLog.getLogs() >> [logResult]

		// Let's make the mocking more flexible
		_ * mockWeb3j.ethGetBlockByNumber(_, _) >> mockBlockRequest
		_ * mockBlockRequest.send() >> mockBlockResponse
		_ * mockBlockResponse.getBlock() >> mockBlockObj
		_ * mockBlockObj.getTimestamp() >> BigInteger.valueOf(Instant.now().epochSecond)
		_ * mockEventRegistry.resolveEventName(_) >> "TestEvent"

		result instanceof BlockingQueue
	}

	def "getPendingTransactions should handle log processing errors"() {
		given:
		def blockHeight = 1000L
		def mockRequest = Mock(Request)
		def mockEthLog = Mock(EthLog)

		def log = new Log()
		log.setTransactionHash("0xabc123")
		log.setBlockNumber("0x3e8")
		log.setLogIndex("0x1")
		log.setTopics(["0xeventSignature"])
		log.setData("0xdata")

		def logResult = Mock(EthLog.LogObject)
		logResult.get() >> log

		when:
		def result = ethEventLogDao.getPendingTransactions(blockHeight)
		Thread.sleep(100)

		then:
		1 * mockWeb3j.ethGetLogs(_) >> mockRequest
		1 * mockRequest.send() >> mockEthLog
		1 * mockEthLog.getLogs() >> [logResult]

		result instanceof BlockingQueue
	}

	def "getPendingTransactions should handle general log processing errors"() {
		given:
		def blockHeight = 1000L
		def mockRequest = Mock(Request)
		def mockEthLog = Mock(EthLog)

		def log = new Log()
		log.setTransactionHash("0xabc123")
		log.setBlockNumber("0x3e8")
		log.setLogIndex("0x1")
		log.setTopics(["0xeventSignature"])
		log.setData("0xdata")

		def logResult = Mock(EthLog.LogObject)
		logResult.get() >> log

		when:
		def result = ethEventLogDao.getPendingTransactions(blockHeight)

		then:
		1 * mockWeb3j.ethGetLogs(_) >> mockRequest
		1 * mockRequest.send() >> mockEthLog
		1 * mockEthLog.getLogs() >> [logResult]

		// Use more flexible error message matching
		_ * mockLogger.error(_, _) // Match any error logging call

		result instanceof BlockingQueue
	}

	def "getPendingTransactions should handle exceptions"() {
		given:
		def blockHeight = 1000L
		def mockRequest = Mock(Request)
		def exception = new IOException("Test exception")

		when:
		def result = ethEventLogDao.getPendingTransactions(blockHeight)

		then:
		1 * mockWeb3j.ethGetLogs(_) >> mockRequest
		1 * mockRequest.send() >> { throw exception }
		1 * mockLogger.error("Error getting filtered logs", exception)

		and: "An empty queue is returned"
		result != null
		result.isEmpty()
	}

	def "getPendingTransactions should log 'Error processing pending transactions' when forceOuterError is true"() {
		given:
		def blockHeight = 1000L
		def mockRequest = Mock(Request)
		def mockEthLog = Mock(EthLog)

		// Create a log that will be processed
		def log = new Log()
		log.setTransactionHash("0xabc123")
		log.setBlockNumber("0x3e8")
		log.setLogIndex("0x1")
		log.setTopics(["0xeventSignature"])

		// Create a log result
		def logResult = Mock(EthLog.LogObject)
		logResult.get() >> log

		// Create a list with our log result
		def logResults = [logResult]

		// Set up normal request/response flow
		mockWeb3j.ethGetLogs(_) >> mockRequest
		mockRequest.send() >> mockEthLog
		mockEthLog.getLogs() >> logResults

		when:
		// Call getPendingTransactions with forceOuterError=true to trigger the outer catch block
		def result = ethEventLogDao.getPendingTransactions(blockHeight, true)

		// Wait for async processing
		sleep(500)

		then:
		result instanceof BlockingQueue
	}

	def "getPendingTransactions should process a log entry correctly"() {
		given:
		def blockHeight = 1000L
		def txHash = "0xabc123"

		// Set up log object
		def log = new Log()
		log.setTransactionHash(txHash)
		log.setBlockNumber("0x3e8")
		log.setLogIndex("0x1")
		log.setTopics(["0xeventSignature"])
		log.setData("0xdata")

		// Set up log result
		def logResult = Mock(EthLog.LogObject)
		logResult.get() >> log

		// Create all mock objects before using them
		def mockRequest = Mock(Request)
		def mockEthLog = Mock(EthLog)
		def mockBlockRequest = Mock(Request)
		def mockBlockResponse = Mock(EthBlock)
		def mockBlockObj = Mock(EthBlock.Block)

		// Set up API mocks
		mockWeb3j.ethGetLogs(_) >> mockRequest
		mockRequest.send() >> mockEthLog
		mockEthLog.getLogs() >> [logResult]

		// Set up block timestamp retrieval
		mockWeb3j.ethGetBlockByNumber(_, _) >> mockBlockRequest
		mockBlockRequest.send() >> mockBlockResponse
		mockBlockResponse.getBlock() >> mockBlockObj
		mockBlockObj.getTimestamp() >> BigInteger.valueOf(1000)

		when:
		def transactionQueue = ethEventLogDao.getPendingTransactions(blockHeight)

		// Wait for async processing to complete
		sleep(500)

		then:
		1 * mockLogger.info("Retrieved {} logs from block height {}", _, _)
		1 * mockLogger.info("Event found tx_hash={}", txHash)

		// Queue should be returned
		transactionQueue instanceof BlockingQueue
	}

	def "should get block timestamp correctly"() {
		given:
		def blockNumber = BigInteger.valueOf(12345)
		def timestamp = BigInteger.valueOf(1626912345)
		def mockRequest = Mock(Request)
		def mockEthBlock = Mock(EthBlock)
		def mockBlock = Mock(EthBlock.Block)

		when:
		// Use reflection to test private method
		def method = EthEventLogDao.class.getDeclaredMethod("getBlockTimestamp", BigInteger.class)
		method.setAccessible(true)

		and: "Set up mocks before invoking method"
		// These need to be set up first
		mockWeb3j.ethGetBlockByNumber(_, _) >> mockRequest
		mockRequest.send() >> mockEthBlock
		mockEthBlock.getBlock() >> mockBlock
		mockBlock.getTimestamp() >> timestamp

		and: "Invoke the method"
		def result = method.invoke(ethEventLogDao, blockNumber)

		then:
		result == timestamp.longValue()
	}


	def "subscribeAll should subscribe to contract events"() {
		given:
		def contractAddress = "******************************************"
		def ethLog = new Log()
		def ethLogFlowable = Flowable.just(ethLog)
		def blockFlowable = Flowable.never()

		_ * mockSubscription.getContractAddresses() >> [contractAddress]
		_ * mockWeb3j.ethLogFlowable(_ as EthFilter) >> ethLogFlowable
		_ * mockWeb3j.blockFlowable(false) >> blockFlowable

		when:
		ethEventLogDao.subscribeAll()

		then:
		noExceptionThrown()
	}


	def "subscribeAll should subscribe to block events"() {
		given:
		def blockFlowable = Flowable.never()

		_ * mockWeb3j.blockFlowable(false) >> blockFlowable

		when:
		ethEventLogDao.subscribeAll()

		then:
		1 * mockWeb3j.blockFlowable(false) >> blockFlowable
		noExceptionThrown()
	}

	def "subscribeAll should skip processing for delayed blocks"() {
		given:
		// Mock a block with a timestamp that will be considered delayed
		def mockBlock = Mock(EthBlock.Block)
		def mockEthBlock = Mock(EthBlock)
		def blockNumber = BigInteger.valueOf(12345)
		def currentTime = Instant.now().epochSecond
		def delayedTimestamp = currentTime - 120 // 2 minutes old (60 seconds allowable diff)

		mockBlock.getNumber() >> blockNumber
		mockBlock.getTimestamp() >> BigInteger.valueOf(delayedTimestamp)
		mockEthBlock.getBlock() >> mockBlock

		// Create a flowable that emits our delayed block
		def blockFlowable = Flowable.just(mockEthBlock)

		// Set up allowable difference
		mockSubscription.getAllowableBlockTimestampDiffSec() >> "60"

		when:
		def result = ethEventLogDao.subscribeAll()

		then:
		1 * mockWeb3j.blockFlowable(false) >> blockFlowable
		result instanceof BlockingQueue

		// Sleep to allow async processing to happen
		sleep(100)

		// Verify warning is logged for delayed block
		1 * mockLogger.warn("Block {} is delayed by more than {} seconds", blockNumber, 60)

		// Verify no further processing happens (no event extraction)
		0 * mockLogger.info("event found, tx_hash: {}", _)
	}

	def "subscribeAll should process non-delayed blocks with events"() {
		given:
		// Mock a block with a recent timestamp (not delayed)
		def mockBlock = Mock(EthBlock.Block)
		def mockEthBlock = Mock(EthBlock)
		def blockNumber = BigInteger.valueOf(12345)
		def currentTime = Instant.now().epochSecond
		def recentTimestamp = currentTime - 30 // Only 30 seconds old (within 60 seconds allowable diff)

		mockBlock.getNumber() >> blockNumber
		mockBlock.getTimestamp() >> BigInteger.valueOf(recentTimestamp)
		mockEthBlock.getBlock() >> mockBlock

		// Mock transaction data for event extraction
		def txHash = "0xabc123"
		def txObject = new EthBlock.TransactionObject()
		txObject.hash = txHash

		def log = new Log()
		log.setTransactionHash(txHash)
		log.setLogIndex("0x1")
		log.setTopics(["0xeventSignature"])
		log.setData("0xdata")
		log.setBlockNumber(blockNumber.toString(16))

		def mockRequest = Mock(Request)
		def mockReceipt = Mock(EthGetTransactionReceipt)
		def mockTxReceipt = Mock(TransactionReceipt)

		// Create a flowable that emits our non-delayed block
		def blockFlowable = Flowable.just(mockEthBlock)

		// Set up allowable difference
		mockSubscription.getAllowableBlockTimestampDiffSec() >> "60"

		when:
		def result = ethEventLogDao.subscribeAll()

		then:
		1 * mockWeb3j.blockFlowable(false) >> blockFlowable
		result instanceof BlockingQueue

		// Mock block transactions for event extraction
		_ * mockBlock.getTransactions() >> [txObject]
		_ * mockWeb3j.ethGetTransactionReceipt(txHash) >> mockRequest
		_ * mockRequest.send() >> mockReceipt
		_ * mockReceipt.getTransactionReceipt() >> Optional.of(mockTxReceipt)
		_ * mockTxReceipt.getLogs() >> [log]

		// Sleep to allow async processing to happen
		sleep(100)

		// Verify non-delayed block is not warned about
		0 * mockLogger.warn("Block {} is delayed by more than {} seconds", _, _)
	}

	def "subscribeAll should handle exceptions during block processing with events"() {
		given:
		// Mock a block with a recent timestamp (not delayed)
		def mockBlock = Mock(EthBlock.Block)
		def mockEthBlock = Mock(EthBlock)
		def blockNumber = BigInteger.valueOf(12345)
		def currentTime = Instant.now().epochSecond
		def recentTimestamp = currentTime - 30 // Not delayed

		mockBlock.getNumber() >> blockNumber
		mockBlock.getTimestamp() >> BigInteger.valueOf(recentTimestamp)
		mockEthBlock.getBlock() >> mockBlock

		// Create a flowable that emits our block
		def blockFlowable = Flowable.just(mockEthBlock)

		// Exception that will be thrown during processing
		def processingException = new RuntimeException("Processing error")

		when:
		def result = ethEventLogDao.subscribeAll()

		then:
		1 * mockWeb3j.blockFlowable(false) >> blockFlowable
		result instanceof BlockingQueue

		// Force extractEvents to throw an exception
		1 * mockBlock.getTransactions() >> { throw processingException }

		// Sleep to allow async processing to happen
		sleep(100)

		// Verify block is not delayed (no warning)
		0 * mockLogger.warn("Block {} is delayed by more than {} seconds", _, _)
	}

	def "subscribeAll should events is empty when processing with events"() {
		given:
		// Mock a block with a recent timestamp (not delayed)
		def mockBlock = Mock(EthBlock.Block)
		def mockEthBlock = Mock(EthBlock)
		def blockNumber = BigInteger.valueOf(0)
		def currentTime = Instant.now().epochSecond
		def recentTimestamp = currentTime - 30 // Not delayed
		def exception = new RuntimeException("Overall async error")


		mockBlock.getNumber() >> blockNumber
		mockBlock.getTimestamp() >> BigInteger.valueOf(recentTimestamp)
		mockEthBlock.getBlock() >> { throw exception }

		// Create a flowable that emits our block
		def blockFlowable = Flowable.just(mockEthBlock)

		// Exception that will be thrown during processing
		//def processingException = new RuntimeException("Processing error")

		when:
		def result = ethEventLogDao.subscribeAll()

		then:
		1 * mockWeb3j.blockFlowable(false) >> blockFlowable
		result instanceof BlockingQueue

		// Sleep to allow async processing to happen
		sleep(100)

		// Verify block is not delayed (no warning)
		0 * mockLogger.warn("Block {} is delayed by more than {} seconds", _, _)
	}

	def "subscribeAll should add transaction to queue when events are found"() {
		given: "A block with events"
		def mockBlock = new EthBlock.Block()
		def mockEthBlock = Mock(EthBlock)
		def blockNumber = BigInteger.valueOf(12345)
		def currentTime = Instant.now().epochSecond
		def timestamp = currentTime - 10 // Recent timestamp

		// Set up block properties
		mockBlock.number = blockNumber
		mockBlock.timestamp = BigInteger.valueOf(timestamp)
		mockEthBlock.getBlock() >> mockBlock

		// Create sample events
		def event = Event.builder()
				.name("TestEvent")
				.transactionHash("0xabc123")
				.blockTimestamp(timestamp)
				.build()
		List<Event> mockEvents = [event]

		// Create a flowable that emits our block
		def blockFlowable = Flowable.just(mockEthBlock)

		and: "A spy that returns events"
		def ethEventLogDaoSpy = Spy(ethEventLogDao) {
			convBlock2EventEntities(mockBlock) >> mockEvents
		}

		when: "Subscribing to all blocks"
		def result = ethEventLogDaoSpy.subscribeAll()

		then: "Web3j is called to get blocks"
		1 * mockWeb3j.blockFlowable(false) >> blockFlowable
		result instanceof BlockingQueue

		// Sleep to allow async processing
		sleep(100)

		and: "Transaction is added to queue"
		result.size() == 1
		def transaction = result.poll()
		transaction.events.size() == 1
		transaction.events[0].name == "TestEvent"
		transaction.blockHeight.blockNumber == blockNumber.longValue()
	}

	def "subscribeAll should not add transaction to queue when no events are found"() {
		given: "A block with no events"
		def mockBlock = new EthBlock.Block()
		def mockEthBlock = Mock(EthBlock)
		def blockNumber = BigInteger.valueOf(12345)
		def currentTime = Instant.now().epochSecond
		def timestamp = currentTime - 10 // Recent timestamp

		// Set up block properties
		mockBlock.number = blockNumber
		mockBlock.timestamp = BigInteger.valueOf(timestamp)
		mockEthBlock.getBlock() >> mockBlock

		// Create a flowable that emits our block
		def blockFlowable = Flowable.just(mockEthBlock)

		and: "A spy that returns empty events list"
		def ethEventLogDaoSpy = Spy(ethEventLogDao) {
			convBlock2EventEntities(mockBlock) >> []
		}

		when: "Subscribing to all blocks"
		def result = ethEventLogDaoSpy.subscribeAll()

		then: "Web3j is called to get blocks"
		1 * mockWeb3j.blockFlowable(false) >> blockFlowable
		result instanceof BlockingQueue

		// Sleep to allow async processing
		sleep(100)

		and: "No transaction is added to queue"
		result.isEmpty()
	}

	def "convertEthLogToEventEntity should successfully convert a log to an event with ABI event"() {
		given: "A valid log with real event data"
		def txHash = "0xabc123"
		def blockNumber = "0x3e8" // 1000 in hex
		def blockTimestamp = 1626912345L
		def contractAddress = "0x1234567890abcdef"

		// Create a properly formatted event signature (topic0) - a real keccak256 hash
		def eventSignature = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef" // Transfer event

		// Create a properly formatted log with real data
		def log = new Log()
		log.transactionHash = txHash
		log.blockNumber = blockNumber
		log.logIndex = "0x1"
		log.topics = [
			eventSignature,
			"0x000000000000000000000000a5cc3c03994db5b0d9a5eedd10cabab0813678ac",
			// from address
			"0x000000000000000000000000c9dd799eb76a14886596836b23a25a70cd05011d"  // to address
		]
		log.address = contractAddress
		log.data = "0x0000000000000000000000000000000000000000000000000de0b6b3a7640000" // amount (1 ETH)

		and: "Mock block data"
		def mockRequest = Mock(Request)
		def mockEthBlock = Mock(EthBlock)
		def mockBlock = Mock(EthBlock.Block)

		and: "Create a real ABI event that matches the log data"
		def eventName = "Transfer"
		def indexedParams = [
			new TypeReference<Address>(true) {},
			new TypeReference<Address>(true) {}
		]
		def nonIndexedParams = [
			new TypeReference<Uint256>(false) {}
		]

		def allParams = []
		allParams.addAll(indexedParams)
		allParams.addAll(nonIndexedParams)

		def abiEvent = new org.web3j.abi.datatypes.Event(eventName, allParams)

		when: "Converting log to event entity"
		def result = ethEventLogDao.convertEthLogToEventEntity(log)

		then: "Web3j is called to get block information"
		1 * mockWeb3j.ethGetBlockByNumber(_, _) >> mockRequest
		1 * mockRequest.send() >> mockEthBlock
		1 * mockEthBlock.getBlock() >> mockBlock
		1 * mockBlock.getTimestamp() >> BigInteger.valueOf(blockTimestamp)

		and: "ABI parser is called to get event definition"
		1 * mockAbiParser.getABIEventByLog(log) >> abiEvent

		and: "Result is correctly built"
		result != null
		result.transactionHash == txHash
		result.logIndex == 1
		result.name == eventName
		result.blockTimestamp == blockTimestamp
	}

	def "convertEthLogToEventEntity should failed convert a log with EventValues is null "() {
		given: "A valid log with real event data"
		def txHash = "0xabc123"
		def blockNumber = "0x3e8" // 1000 in hex
		def contractAddress = "0x1234567890abcdef"

		// Create a properly formatted event signature (topic0) - a real keccak256 hash
		def eventSignature = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef" // Transfer event

		// Create a properly formatted log with real data
		def log = new Log()
		log.transactionHash = txHash
		log.blockNumber = blockNumber
		log.logIndex = "0x1"
		log.topics = [
			eventSignature,
			"0x000000000000000000000000a5cc3c03994db5b0d9a5eedd10cabab0813678ac",
			// from address
			"0x000000000000000000000000c9dd799eb76a14886596836b23a25a70cd05011d"  // to address
		]
		log.address = contractAddress
		log.data = "0x0000000000000000000000000000000000000000000000000de0b6b3a7640000" // amount (1 ETH)

		and: "Mock block data"
		def mockRequest = Mock(Request)
		def mockEthBlock = Mock(EthBlock)
		def mockBlock = Mock(EthBlock.Block)

		and: "Create a real ABI event that matches the log data"
		def eventName = "Transfer"
		def indexedParams = [
			new TypeReference<Address>() {} as TypeReference<Type>
		]
		def nonIndexedParams = [
			new TypeReference<Address>() {} as TypeReference<Type>
		]

		def allParams = []
		allParams.addAll(indexedParams)
		allParams.addAll(nonIndexedParams)

		def abiEvent = new org.web3j.abi.datatypes.Event(eventName, allParams)

		when: "Converting log to event entity"
		ethEventLogDao.convertEthLogToEventEntity(log)

		then: "Web3j is called to get block information"
		1 * mockWeb3j.ethGetBlockByNumber(_, _) >> mockRequest
		1 * mockRequest.send() >> mockEthBlock
		1 * mockEthBlock.getBlock() >> mockBlock

		and: "ABI parser is called to get event definition"
		1 * mockAbiParser.getABIEventByLog(log) >> abiEvent

		and: "Exception is thrown"
		thrown(Exception)
	}

	def "convertEthLogToEventEntity should handle null ABI event"() {
		given: "A valid log"
		def txHash = "0xabc123"
		def blockNumber = "0x3e8" // 1000 in hex
		def contractAddress = "0x1234567890abcdef"
		def eventSignature = "0xeventSignature"

		def log = new Log()
		log.transactionHash = txHash
		log.blockNumber = blockNumber
		log.logIndex = "0x1"
		log.topics = [eventSignature]
		log.address = contractAddress

		and: "Mock block data"
		def mockRequest = Mock(Request)
		def mockEthBlock = Mock(EthBlock)
		def mockBlock = Mock(EthBlock.Block)

		when: "Converting log to event entity"
		ethEventLogDao.convertEthLogToEventEntity(log)

		then: "Web3j is called to get block information"
		1 * mockWeb3j.ethGetBlockByNumber(_, _) >> mockRequest
		1 * mockRequest.send() >> mockEthBlock
		1 * mockEthBlock.getBlock() >> mockBlock

		and: "ABI parser is called to get event definition"
		1 * mockAbiParser.getABIEventByLog(log) >> null

		and: "Error is logged"
		1 * mockLogger.info("Event definition not found in ABI")

		and: "Exception is thrown"
		thrown(Exception)
	}

	def "convertEthLogToEventEntity should handle block retrieval exception"() {
		given: "A valid log"
		def log = new Log()
		log.blockNumber = "0x3e8"
		log.topics = ["0xeventSignature"]

		and: "Mock that throws exception"
		def exception = new IOException("Test exception")
		def mockRequest = Mock(Request)

		when: "Converting log to event entity"
		ethEventLogDao.convertEthLogToEventEntity(log)

		then: "Web3j is called and throws exception"
		1 * mockWeb3j.ethGetBlockByNumber(_, _) >> mockRequest
		1 * mockRequest.send() >> { throw exception }

		and: "Exception is propagated"
		thrown(Exception)
	}

	def "convertEthLogToEventEntity should handle ABI parser exception"() {
		given: "A valid log"
		def log = new Log()
		log.blockNumber = "0x3e8"
		log.topics = ["0xeventSignature"]

		and: "Mock block data"
		def mockRequest = Mock(Request)
		def mockEthBlock = Mock(EthBlock)
		def mockBlock = Mock(EthBlock.Block)

		and: "ABI parser throws exception"
		def exception = new Exception("ABI parsing error")

		when: "Converting log to event entity"
		ethEventLogDao.convertEthLogToEventEntity(log)

		then: "Web3j is called to get block information"
		1 * mockWeb3j.ethGetBlockByNumber(_, _) >> mockRequest
		1 * mockRequest.send() >> mockEthBlock
		1 * mockEthBlock.getBlock() >> mockBlock

		and: "ABI parser throws exception"
		1 * mockAbiParser.getABIEventByLog(log) >> { throw exception }

		and: "Exception is propagated"
		thrown(Exception)
	}

	def "convertEthLogToEventEntity should handle empty topics list"() {
		given: "A log with empty topics"
		def log = new Log()
		log.blockNumber = "0x3e8"
		log.topics = []

		and: "Mock block data"
		def mockRequest = Mock(Request)
		def mockEthBlock = Mock(EthBlock)
		def mockBlock = Mock(EthBlock.Block)

		when: "Converting log to event entity"
		ethEventLogDao.convertEthLogToEventEntity(log)

		then: "Web3j is called to get block information"
		1 * mockWeb3j.ethGetBlockByNumber(_, _) >> mockRequest
		1 * mockRequest.send() >> mockEthBlock
		1 * mockEthBlock.getBlock() >> mockBlock

		and: "Exception is propagated"
		thrown(Exception)
	}

	def "convBlock2EventEntities should process events from a block with logs"() {
		given: "A block with transactions"
		def txHash = "0xabc123"
		def blockTimestamp = 1626912345L

		def txObject = new EthBlock.TransactionObject()
		txObject.hash = txHash

		def blockObj = new EthBlock.Block()
		blockObj.timestamp = BigInteger.valueOf(blockTimestamp)
		blockObj.number = BigInteger.valueOf(1000)
		blockObj.transactions = [txObject]

		and: "A log for this transaction"
		def log = new Log()
		log.transactionHash = txHash
		log.topics = ["0xevent123"]
		log.address = "0x1234567890abcdef"
		log.blockNumber = "0x3e8"
		log.logIndex = "0x1"

		and: "Mocked API responses"
		def mockRequest = Mock(Request)
		def mockFuture = Mock(CompletableFuture)
		def mockReceipt = Mock(EthGetTransactionReceipt)
		def mockTxReceipt = Mock(TransactionReceipt)

		and: "Expected event to be returned"
		def expectedEvent = Event.builder()
				.name("TestEvent")
				.transactionHash(txHash)
				.blockTimestamp(blockTimestamp)
				.build()

		and: "A spy that returns the expected event"
		def ethEventLogDaoSpy = Spy(ethEventLogDao) {
			convertEthLogToEventEntity(_) >> expectedEvent
		}

		when: "Converting the block to events"
		def result = ethEventLogDaoSpy.convBlock2EventEntities(blockObj)

		then: "The Web3j API is called correctly"
		1 * mockWeb3j.ethGetTransactionReceipt(txHash) >> mockRequest
		1 * mockRequest.sendAsync() >> mockFuture
		1 * mockFuture.get() >> mockReceipt
		1 * mockReceipt.getTransactionReceipt() >> Optional.of(mockTxReceipt)
		1 * mockTxReceipt.getLogs() >> [log]

		and: "The result contains the expected event"
		result instanceof List
		result.size() == 1
		result[0].name == "TestEvent"
		result[0].transactionHash == txHash
	}

	def "subscribeAll should handle NumberFormatException when parsing allowable timestamp difference"() {
		given: "A configuration with invalid allowable timestamp difference"
		// Create a new mock for properties to avoid affecting other tests
		def localMockProperties = Mock(BcmonitoringConfigurationProperties)
		def localMockSubscription = Mock(BcmonitoringConfigurationProperties.Subscription)

		// Set up the mock to throw NumberFormatException
		localMockProperties.getSubscription() >> localMockSubscription
		localMockSubscription.getAllowableBlockTimestampDiffSec() >> "NotANumber"

		// Create a new dao with our mocked properties
		def localDao = new EthEventLogDao(
				mockLogger,
				localMockProperties,
				mockWeb3jConfig,
				mockEventRegistry,
				mockAbiParser,
				mockObjectMapper
				)

		when: "Calling subscribeAll"
		def result = localDao.subscribeAll()

		then: "The NumberFormatException is caught and logged"
		1 * mockLogger.error("Failed to parse allowable timestamp difference", _ as NumberFormatException)

		and: "The method returns null"
		result == null
	}

	def "subscribeAll should log subscription error"() {
		given: "A subscription that will emit an error"
		def subscriptionError = new RuntimeException("Test subscription error")
		def errorFlowable = Flowable.error(subscriptionError)

		// Create a new dao with our mocked dependencies
		def localWeb3j = Mock(Web3j)
		def localWeb3jConfig = Mock(Web3jConfig)
		def localDao = new EthEventLogDao(
				mockLogger,
				mockProperties,
				localWeb3jConfig,
				mockEventRegistry,
				mockAbiParser,
				mockObjectMapper
				)

		// Set up mocks
		localWeb3j.blockFlowable(false) >> errorFlowable
		localWeb3jConfig.createWebSocketWeb3j() >> localWeb3j

		when: "Calling subscribeAll"
		def result = localDao.subscribeAll()

		then: "The subscription error is logged with the exact message 'Subscription error'"
		1 * mockLogger.error("Subscription error", subscriptionError)

		and: "The method returns a queue"
		result instanceof BlockingQueue
	}

	def "subscribeAll should handle exception during Web3j subscription creation"() {
		given: "A Web3jConfig that throws an exception during subscription creation"
		def subscriptionError = new RuntimeException("Failed to create subscription")

		// Create a new dao with our mocked dependencies
		def localWeb3jConfig = Mock(Web3jConfig)
		def localDao = new EthEventLogDao(
				mockLogger,
				mockProperties,
				localWeb3jConfig,
				mockEventRegistry,
				mockAbiParser,
				mockObjectMapper
				)

		// Set up mocks to throw exception during subscription creation
		localWeb3jConfig.createWebSocketWeb3j() >> { throw subscriptionError }

		when: "Calling subscribeAll"
		def result = localDao.subscribeAll()

		then: "The error is logged with the exact message 'Failed to create Web3j subscription'"
		1 * mockLogger.error("Failed to create Web3j subscription", subscriptionError)

		and: "The method returns an empty queue"
		result instanceof BlockingQueue
		result.isEmpty()
	}
}
