package com.decurret_dcp.dcjpy.bcmonitoring.infrastructure.mock

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventRepository
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event
import spock.mock.DetachedMockFactory

class EventRepositoryMock {
	static final detachedMockFactory = new DetachedMockFactory()

	static EventRepository createMock() {
		return detachedMockFactory.Mock(EventRepository)
	}

	static EventRepository withSuccessfulSave() {
		def mock = createMock()
		mock.save({ Event event -> true } as Event) >> {}
		return mock
	}

	static EventRepository withFailedSave() {
		def mock = createMock()
		mock.save({ Event event -> false } as Event) >> {}
		return mock
	}
}
