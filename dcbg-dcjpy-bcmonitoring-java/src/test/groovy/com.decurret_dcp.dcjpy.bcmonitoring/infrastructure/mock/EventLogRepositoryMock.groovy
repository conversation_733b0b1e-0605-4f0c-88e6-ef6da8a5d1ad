package com.decurret_dcp.dcjpy.bcmonitoring.infrastructure.mock

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.sqs.EventLogRepository
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executor

class EventLogRepositoryMock {
	static EventLogRepository createMock() {
		return Mock(EventLogRepository)
	}

	static EventLogRepository withSuccessfulSubscription(Transaction transaction) {
		def mock = createMock()
		mock.subscribe() >> CompletableFuture.supplyAsync({
			[transaction]
		}, Thread.ofVirtual().factory() as Executor)
		return mock
	}
}
