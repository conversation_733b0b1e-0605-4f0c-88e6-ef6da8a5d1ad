package com.decurret_dcp.dcjpy.bcmonitoring.infrastructure.mock

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3AbiRepository
import software.amazon.awssdk.services.s3.model.*

class S3AbiRepositoryMock{
	static S3AbiRepository createMock() {
		return Mock(S3AbiRepository)
	}

	static S3AbiRepository withPrefixes(String bucketName, String... prefixes) {
		def mock = createMock()

		def commonPrefixes = prefixes.collect { prefix ->
			CommonPrefix.builder().prefix(prefix).build()
		}

		def response = ListObjectsV2Response.builder()
				.commonPrefixes(commonPrefixes)
				.build()

		mock.listCommonPrefixesObjects(bucketName) >> response

		return mock
	}

	static S3AbiRepository withObjects(String bucketName, String prefix, String... keys) {
		def mock = createMock()

		def objects = keys.collect { key ->
			S3Object.builder().key(key).build()
		}

		def response = ListObjectsV2Response.builder()
				.contents(objects)
				.build()

		mock.listObjects(bucketName, prefix) >> response

		return mock
	}

	static S3AbiRepository withObject(String bucketName, String key, String content) {
		def mock = createMock()

		def response = GetObjectResponse.builder().build()
		mock.getObject(bucketName, key) >> response

		return mock
	}
}
