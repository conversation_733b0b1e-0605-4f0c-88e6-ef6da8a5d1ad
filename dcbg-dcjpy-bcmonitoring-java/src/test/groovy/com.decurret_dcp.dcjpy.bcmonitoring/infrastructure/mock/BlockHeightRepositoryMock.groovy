package com.decurret_dcp.dcjpy.bcmonitoring.infrastructure.mock

import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight

class BlockHeightRepository {
	static BlockHeightRepository createMock() {
		return Mock(BlockHeightRepository)
	}

	static BlockHeightRepository withBlockHeight(Long height) {
		def mock = createMock()
		def blockHeight = new BlockHeight(height)
		mock.get() >> blockHeight
		mock.save({ BlockHeight bHeight -> true }) >> {}

		return mock
	}

	static BlockHeightRepository withGetError() {
		def mock = createMock()
		mock.get() >> { throw new RuntimeException("Failed to get block height") }
		return mock
	}

	static BlockHeightRepository withSaveError() {
		def mock = createMock()
		mock.get() >> new BlockHeight(1000L)
		mock.save({ BlockHeight blockHeight -> false }) >> {}

		return mock
	}

	static BlockHeightRepository withUpdateSequence(Long initialHeight, Long updatedHeight) {
		def mock = createMock()
		mock.get() >>> [
			new BlockHeight(initialHeight),
			new BlockHeight(updatedHeight)
		]
		mock.save({ BlockHeight blockHeight -> true }) >> {}

		return mock
	}
}
