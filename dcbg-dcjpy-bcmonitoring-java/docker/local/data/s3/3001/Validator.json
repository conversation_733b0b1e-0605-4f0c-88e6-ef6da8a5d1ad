{"address": "0xDC628B6035360Ee1521060D85B35a1315056b5f5", "abi": [{"type": "event", "anonymous": false, "name": "AddAccount", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "bool", "name": "identified", "indexed": false}, {"type": "bool", "name": "enabled", "indexed": false}, {"type": "uint256[]", "name": "limitAmounts"}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddValidator", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": true}, {"type": "bytes32", "name": "issuerId", "indexed": false}, {"type": "bytes32", "name": "name", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddValidatorAccountId", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddValidatorRole", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": true}, {"type": "address", "name": "validatorEoa", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "ModValidator", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": true}, {"type": "bytes32", "name": "name", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SetBizZoneTerminated", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "uint16", "name": "zoneId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SetTerminated", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "bytes32", "name": "reasonCode", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SyncAccount", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": false}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "string", "name": "accountName", "indexed": false}, {"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "string", "name": "zoneName", "indexed": false}, {"type": "bytes32", "name": "accountStatus", "indexed": false}, {"type": "uint256", "name": "approvalAmount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "ValidatorEnabled", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": true}, {"type": "bool", "name": "enabled", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "ROLE_PREFIX_VALIDATOR", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": ""}]}, {"type": "function", "name": "addAccount", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "uint256[]", "name": "limitAmounts"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "addValidator", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "addValidatorAccountId", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "addValidatorRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "address", "name": "validatorEoa"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "getAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "accountData", "components": [{"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}, {"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "accountDataAll", "components": [{"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}, {"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}, {"type": "tuple[]", "name": "businessZoneAccounts", "components": [{"type": "string", "name": "accountName"}, {"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountList", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "string", "name": "sortOrder"}], "outputs": [{"type": "tuple[]", "name": "accounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getDestinationAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "string", "name": "accountName"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getValidator", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}], "outputs": [{"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "issuerId"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getValidatorAccountId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}], "outputs": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getValidatorAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "tuple", "name": "validator", "components": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "validatorAccountId"}, {"type": "bool", "name": "enabled"}, {"type": "bool", "name": "validatorIdExistence"}, {"type": "bool", "name": "issuerIdLinkedFlag"}, {"type": "address", "name": "validatorEoa"}, {"type": "tuple[]", "name": "validAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByValidatorId"}]}]}]}, {"type": "function", "name": "getValidatorCount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint256", "name": "count"}]}, {"type": "function", "name": "getValidatorId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getValidatorList", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "offset"}], "outputs": [{"type": "tuple[]", "name": "validators", "components": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "issuerId"}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getZoneByAccountId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple[]", "name": "zones", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "hasAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "hasValidator", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "hasValidatorByAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}]}, {"type": "function", "name": "hasValidatorRole", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "hash"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "modAccount", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "modValidator", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setActiveBusinessAccountWithZone", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "setBizZoneTerminated", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "setTerminated", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "setValidatorAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "validator", "components": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "validatorAccountId"}, {"type": "bool", "name": "enabled"}, {"type": "bool", "name": "validatorIdExistence"}, {"type": "bool", "name": "issuerIdLinkedFlag"}, {"type": "address", "name": "validatorEoa"}, {"type": "tuple[]", "name": "validAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByValidatorId"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "syncAccount", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint256", "name": "approvalAmount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0xa602b50e1e4ebafd41ef446094d6dab769ef89cb2b1f3aef4f23f1a050b291e1", "receipt": {"to": null, "from": "0xC8b9859901F2ec78750573cb95F74C90F627FD02", "contractAddress": "0xDC628B6035360Ee1521060D85B35a1315056b5f5", "transactionIndex": 0, "gasUsed": "5584956", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xa45993b70d2d2528c10b03b9d912a8cada87bbaaca00087cfa04d2da057959bd", "blockNumber": 214, "cumulativeGasUsed": "5584956", "status": 1}}