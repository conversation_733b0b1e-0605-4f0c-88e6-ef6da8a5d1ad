{"address": "0xd85B74739fD550B3BD3368C4232BCD9c188B6366", "abi": [{"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "checkExchange", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "fromZoneId"}, {"type": "uint16", "name": "toZoneId"}, {"type": "uint256", "name": "amount"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkFinAccountStatus", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bytes32", "name": "accountStatus"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkSyncAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "bytes", "name": "accountSignature"}, {"type": "bytes", "name": "info"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkTransaction", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "bytes", "name": "accountSignature"}, {"type": "bytes", "name": "info"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getAccountLimit", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "accountLimitData", "components": [{"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getBizZoneAccountStatus", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "zoneId"}], "outputs": [{"type": "bytes32", "name": "accountStatus"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x37ec833dedb36e2e084b0f1e33b3a14a71a41bf63c4e23a7c79871aca0b4042f", "receipt": {"to": null, "from": "0x921B538f857bEEca1a33Fe66e90fE71a5F3F707b", "contractAddress": "0xd85B74739fD550B3BD3368C4232BCD9c188B6366", "transactionIndex": 0, "gasUsed": "3079118", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x6d75dbfe59a9df43aa14d3fb9d26c13ec42166076ccf98ecf628586b001ec38f", "blockNumber": 98, "cumulativeGasUsed": "3079118", "status": 1}}