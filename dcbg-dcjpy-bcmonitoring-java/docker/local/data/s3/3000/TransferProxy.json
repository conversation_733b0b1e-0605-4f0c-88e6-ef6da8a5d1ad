{"address": "0x1ad5b80e7Dd86D2D49b5055D8f7969eB92D6169A", "abi": [{"type": "event", "anonymous": false, "name": "AddRule", "inputs": [{"type": "address", "name": "rule", "indexed": false}, {"type": "uint256", "name": "position", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "DeleteRule", "inputs": [{"type": "address", "name": "rule", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "addRule", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "rule"}, {"type": "uint256", "name": "position"}], "outputs": []}, {"type": "function", "name": "clearRule", "constant": false, "payable": false, "inputs": [], "outputs": []}, {"type": "function", "name": "customTransfer", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "string", "name": "memo"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "deleteRule", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "rule"}], "outputs": []}, {"type": "function", "name": "findAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address[]", "name": "rules"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}, {"type": "address", "name": "token"}], "outputs": []}, {"type": "function", "name": "isRegistered", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "address", "name": "rule"}], "outputs": [{"type": "bool", "name": "result"}]}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0xe4327c64e471aa8fbbef80eefb9e7c036031727e2556ae1a90465a27ab36242c", "receipt": {"to": null, "from": "0x921B538f857bEEca1a33Fe66e90fE71a5F3F707b", "contractAddress": "0x1ad5b80e7Dd86D2D49b5055D8f7969eB92D6169A", "transactionIndex": 0, "gasUsed": "1234482", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xd230d4ff1b09ca0066213b4702edfd954666114b93db211f692b4ed13bf72c55", "blockNumber": 102, "cumulativeGasUsed": "1234482", "status": 1}}