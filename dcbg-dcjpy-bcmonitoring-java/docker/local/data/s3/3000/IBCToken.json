{"address": "0x7B65fDBbca26077CFa799A0Ac7BFeD53Aa290C12", "abi": [{"type": "event", "anonymous": false, "name": "DischargeRequested", "inputs": [{"type": "bytes32", "name": "validatorId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint16", "name": "fromZoneId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "IssueVoucher", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": true}, {"type": "bytes32", "name": "validatorId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "string", "name": "accountName", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "balance", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "RedeemVoucher", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": true}, {"type": "bytes32", "name": "validatorId", "indexed": true}, {"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "string", "name": "accountName", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "balance", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Transfer", "inputs": [{"type": "tuple", "name": "transferData", "indexed": false, "components": [{"type": "bytes32", "name": "transferType"}, {"type": "uint16", "name": "zoneId"}, {"type": "bytes32", "name": "fromValidatorId"}, {"type": "bytes32", "name": "toValidatorId"}, {"type": "uint256", "name": "fromAccountBalance"}, {"type": "uint256", "name": "toAccountBalance"}, {"type": "uint256", "name": "businessZoneBalance"}, {"type": "uint16", "name": "bizZoneId"}, {"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "string", "name": "fromAccountName"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "string", "name": "toAccountName"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "string", "name": "memo"}]}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "checkAdminRole", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "hash"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "has"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "discharge", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "fromZoneId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "initAccountBalance", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": []}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "issueVoucher", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "redeemVoucher", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "syncBusinessZoneBalance", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "params", "components": [{"type": "bytes32", "name": "fromAccountId"}, {"type": "string", "name": "fromAccountName"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "string", "name": "toAccountName"}, {"type": "uint16", "name": "fromZoneId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}]}], "outputs": []}, {"type": "function", "name": "transferFromEscrow", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "fromZoneId"}, {"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "transferToEscrow", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "fromZoneId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x80c765cd27c68439e7a0a87b72598e2793e275a701fb0cdb39cac71c7f906825", "receipt": {"to": null, "from": "0x921B538f857bEEca1a33Fe66e90fE71a5F3F707b", "contractAddress": "0x7B65fDBbca26077CFa799A0Ac7BFeD53Aa290C12", "transactionIndex": 0, "gasUsed": "3153689", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xe4768ea026e4d51c5453784ac3db25fe698451a1b8cbe3b589db4c2bea4ea744", "blockNumber": 94, "cumulativeGasUsed": "3153689", "status": 1}}