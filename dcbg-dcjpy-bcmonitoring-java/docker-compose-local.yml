services:
  bc-monitoring:
    build:
      context: .
      dockerfile: Dockerfile
      target: build
    profiles: ["fin", "biz"]
    tty: true
    entrypoint: ["bc_monitoring"]
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    environment:
      - WEBSOCKET_URI_HOST=host.docker.internal
      - WEBSOCKET_URI_PORT=${WEBSOCKET_URI_PORT}
      - DYNAMODB_ENDPOINT=http://host.docker.internal:${DYNAMODB_PORT}
      - DYNAMODB_REGION=ap-northeast-1
      - DYNAMODB_TABLE_NAME_PREFIX=local
      - LOCAL_S3_URL=http://host.docker.internal:${S3_PORT}
      - S3_BUCKET_NAME=abijson
      - S3_REGION=ap-northeast-1
      - AWS_ACCESS_KEY=dummy
      - AWS_SECRET_KEY=dummy
      - SUBSCRIPTION_CHECK_INTERVAL=3000
      - ALL<PERSON>ABLE_BLOCK_TIMESTAMP_DIFF_SEC=2
      - EVENTS_TABLE_NAME=Events
      - BLOCK_HEIGHT_TABLE_NAME=BlockHeight
      - ENV=local
      - ABI_FORMAT=hardhat
